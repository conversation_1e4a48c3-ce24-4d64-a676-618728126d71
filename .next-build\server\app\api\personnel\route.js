/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/personnel/route";
exports.ids = ["app/api/personnel/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fpersonnel%2Froute&page=%2Fapi%2Fpersonnel%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fpersonnel%2Froute.ts&appDir=C%3A%5CUsers%5CDABBIE%5COneDrive%5CDesktop%5Clgu-project-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDABBIE%5COneDrive%5CDesktop%5Clgu-project-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fpersonnel%2Froute&page=%2Fapi%2Fpersonnel%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fpersonnel%2Froute.ts&appDir=C%3A%5CUsers%5CDABBIE%5COneDrive%5CDesktop%5Clgu-project-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDABBIE%5COneDrive%5CDesktop%5Clgu-project-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_DABBIE_OneDrive_Desktop_lgu_project_app_src_app_api_personnel_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/personnel/route.ts */ \"(rsc)/./src/app/api/personnel/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/personnel/route\",\n        pathname: \"/api/personnel\",\n        filename: \"route\",\n        bundlePath: \"app/api/personnel/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\app\\\\api\\\\personnel\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_DABBIE_OneDrive_Desktop_lgu_project_app_src_app_api_personnel_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fpersonnel%2Froute&page=%2Fapi%2Fpersonnel%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fpersonnel%2Froute.ts&appDir=C%3A%5CUsers%5CDABBIE%5COneDrive%5CDesktop%5Clgu-project-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDABBIE%5COneDrive%5CDesktop%5Clgu-project-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/personnel/route.ts":
/*!****************************************!*\
  !*** ./src/app/api/personnel/route.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _utils_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/utils/supabase/server */ \"(rsc)/./src/utils/supabase/server.ts\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db.ts\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/dist/esm/index.js\");\n\n\n\n\nconst createPersonnelSchema = zod__WEBPACK_IMPORTED_MODULE_3__.z.object({\n    name: zod__WEBPACK_IMPORTED_MODULE_3__.z.string().min(1, 'Name is required'),\n    email: zod__WEBPACK_IMPORTED_MODULE_3__.z.string().email('Invalid email address'),\n    phone: zod__WEBPACK_IMPORTED_MODULE_3__.z.string().optional(),\n    address: zod__WEBPACK_IMPORTED_MODULE_3__.z.string().optional(),\n    profilePhoto: zod__WEBPACK_IMPORTED_MODULE_3__.z.string().optional(),\n    department: zod__WEBPACK_IMPORTED_MODULE_3__.z.string().min(1, 'Department is required'),\n    position: zod__WEBPACK_IMPORTED_MODULE_3__.z.string().optional(),\n    hireDate: zod__WEBPACK_IMPORTED_MODULE_3__.z.string().optional(),\n    status: zod__WEBPACK_IMPORTED_MODULE_3__.z[\"enum\"]([\n        'Active',\n        'Inactive',\n        'On Leave',\n        'Suspended'\n    ]).default('Active'),\n    biography: zod__WEBPACK_IMPORTED_MODULE_3__.z.string().optional(),\n    spouseName: zod__WEBPACK_IMPORTED_MODULE_3__.z.string().optional(),\n    spouseOccupation: zod__WEBPACK_IMPORTED_MODULE_3__.z.string().optional(),\n    childrenCount: zod__WEBPACK_IMPORTED_MODULE_3__.z.string().optional(),\n    emergencyContact: zod__WEBPACK_IMPORTED_MODULE_3__.z.string().optional(),\n    childrenNames: zod__WEBPACK_IMPORTED_MODULE_3__.z.string().optional()\n});\nasync function GET(request) {\n    try {\n        const supabase = await (0,_utils_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createClient)();\n        const { data: { user } } = await supabase.auth.getUser();\n        if (!user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        const { searchParams } = new URL(request.url);\n        const page = parseInt(searchParams.get('page') || '1');\n        const limit = parseInt(searchParams.get('limit') || '10');\n        const search = searchParams.get('search') || '';\n        const department = searchParams.get('department') || '';\n        const status = searchParams.get('status') || '';\n        const sort = searchParams.get('sort') || 'name_asc';\n        console.log(`[API] GET /api/personnel - Page: ${page}, Limit: ${limit}, Search: \"${search}\", Department: \"${department}\", Status: \"${status}\", Sort: \"${sort}\"`);\n        // Use the new database service with built-in filtering and pagination\n        const result = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.getAllPersonnel(page, limit, {\n            search: search || undefined,\n            department: department || undefined,\n            status: status || undefined,\n            sort: sort\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            personnel: result.data,\n            pagination: result.pagination\n        });\n    } catch (error) {\n        console.error('[API] Error fetching personnel:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error',\n            details: error instanceof Error ? error.message : 'Unknown error'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function POST(request) {\n    try {\n        const supabase = await (0,_utils_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createClient)();\n        const { data: { user } } = await supabase.auth.getUser();\n        if (!user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        const body = await request.json();\n        const validatedData = createPersonnelSchema.parse(body);\n        const existingPersonnel = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.findPersonnelByEmail(validatedData.email);\n        if (existingPersonnel) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Personnel with this email already exists'\n            }, {\n                status: 400\n            });\n        }\n        const personnel = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.createPersonnel({\n            name: validatedData.name,\n            email: validatedData.email,\n            phone: validatedData.phone || null,\n            address: validatedData.address || null,\n            profile_photo: validatedData.profilePhoto || null,\n            department: validatedData.department,\n            position: validatedData.position || null,\n            hire_date: validatedData.hireDate || null,\n            status: validatedData.status,\n            biography: validatedData.biography || null,\n            spouse_name: validatedData.spouseName || null,\n            spouse_occupation: validatedData.spouseOccupation || null,\n            children_count: validatedData.childrenCount || null,\n            emergency_contact: validatedData.emergencyContact || null,\n            children_names: validatedData.childrenNames || null\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(personnel, {\n            status: 201\n        });\n    } catch (error) {\n        if (error instanceof zod__WEBPACK_IMPORTED_MODULE_3__.z.ZodError) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Validation error',\n                details: error.errors\n            }, {\n                status: 400\n            });\n        }\n        console.error('Error creating personnel:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/personnel/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database.ts":
/*!*****************************!*\
  !*** ./src/lib/database.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DatabaseService: () => (/* binding */ DatabaseService),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _supabaseService__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabaseService */ \"(rsc)/./src/lib/supabaseService.ts\");\n/**\n * Enterprise Database Service Layer\n * This replaces the mock database with real Supabase integration\n * Provides a unified interface for all database operations\n */ \n/**\n * Main Database Service Class\n * This class provides the same interface as MockDatabase but uses Supabase\n */ class DatabaseService {\n    // =====================================================\n    // USER OPERATIONS\n    // =====================================================\n    /**\n   * Find user by email address\n   * @param email - User's email address\n   * @returns User object or null if not found\n   */ static async findUserByEmail(email) {\n        try {\n            return await _supabaseService__WEBPACK_IMPORTED_MODULE_0__.SupabaseService.findUserByEmail(email);\n        } catch (error) {\n            console.error('[DatabaseService] Error in findUserByEmail:', error);\n            throw error;\n        }\n    }\n    /**\n   * Find user by ID\n   * @param id - User's ID\n   * @returns User object or null if not found\n   */ static async findUserById(id) {\n        try {\n            return await _supabaseService__WEBPACK_IMPORTED_MODULE_0__.SupabaseService.findUserById(id);\n        } catch (error) {\n            console.error('[DatabaseService] Error in findUserById:', error);\n            throw error;\n        }\n    }\n    /**\n   * Create a new user\n   * @param userData - User data to insert (without id, created_at, updated_at)\n   * @returns Created user object\n   */ static async createUser(userData) {\n        try {\n            return await _supabaseService__WEBPACK_IMPORTED_MODULE_0__.SupabaseService.createUser(userData);\n        } catch (error) {\n            console.error('[DatabaseService] Error in createUser:', error);\n            throw error;\n        }\n    }\n    /**\n   * Update user data\n   * @param id - User ID to update\n   * @param userData - Partial user data to update\n   * @returns Updated user object or null if not found\n   */ static async updateUser(id, userData) {\n        try {\n            return await _supabaseService__WEBPACK_IMPORTED_MODULE_0__.SupabaseService.updateUser(id, userData);\n        } catch (error) {\n            console.error('[DatabaseService] Error in updateUser:', error);\n            throw error;\n        }\n    }\n    /**\n   * Delete user by ID\n   * @param id - User ID to delete\n   * @returns True if deleted successfully\n   */ static async deleteUser(id) {\n        try {\n            return await _supabaseService__WEBPACK_IMPORTED_MODULE_0__.SupabaseService.deleteUser(id);\n        } catch (error) {\n            console.error('[DatabaseService] Error in deleteUser:', error);\n            throw error;\n        }\n    }\n    /**\n   * Get all users with optional filtering\n   * @param options - Query options\n   * @returns Array of users\n   */ static async getAllUsers(options) {\n        try {\n            return await _supabaseService__WEBPACK_IMPORTED_MODULE_0__.SupabaseService.getAllUsers(options);\n        } catch (error) {\n            console.error('[DatabaseService] Error in getAllUsers:', error);\n            throw error;\n        }\n    }\n    // =====================================================\n    // PERSONNEL OPERATIONS\n    // =====================================================\n    /**\n   * Find personnel by email address\n   * @param email - Personnel's email address\n   * @returns Personnel object or null if not found\n   */ static async findPersonnelByEmail(email) {\n        try {\n            return await _supabaseService__WEBPACK_IMPORTED_MODULE_0__.SupabaseService.findPersonnelByEmail(email);\n        } catch (error) {\n            console.error('[DatabaseService] Error in findPersonnelByEmail:', error);\n            throw error;\n        }\n    }\n    /**\n   * Find personnel by ID\n   * @param id - Personnel's ID\n   * @returns Personnel object or null if not found\n   */ static async findPersonnelById(id) {\n        try {\n            return await _supabaseService__WEBPACK_IMPORTED_MODULE_0__.SupabaseService.findPersonnelById(id);\n        } catch (error) {\n            console.error('[DatabaseService] Error in findPersonnelById:', error);\n            throw error;\n        }\n    }\n    /**\n   * Create new personnel record\n   * @param personnelData - Personnel data to insert (without id, created_at, updated_at)\n   * @returns Created personnel object\n   */ static async createPersonnel(personnelData) {\n        try {\n            return await _supabaseService__WEBPACK_IMPORTED_MODULE_0__.SupabaseService.createPersonnel(personnelData);\n        } catch (error) {\n            console.error('[DatabaseService] Error in createPersonnel:', error);\n            throw error;\n        }\n    }\n    /**\n   * Update personnel data\n   * @param id - Personnel ID to update\n   * @param personnelData - Partial personnel data to update\n   * @returns Updated personnel object or null if not found\n   */ static async updatePersonnel(id, personnelData) {\n        try {\n            return await _supabaseService__WEBPACK_IMPORTED_MODULE_0__.SupabaseService.updatePersonnel(id, personnelData);\n        } catch (error) {\n            console.error('[DatabaseService] Error in updatePersonnel:', error);\n            throw error;\n        }\n    }\n    /**\n   * Delete personnel by ID\n   * @param id - Personnel ID to delete\n   * @returns True if deleted successfully\n   */ static async deletePersonnel(id) {\n        try {\n            return await _supabaseService__WEBPACK_IMPORTED_MODULE_0__.SupabaseService.deletePersonnel(id);\n        } catch (error) {\n            console.error('[DatabaseService] Error in deletePersonnel:', error);\n            throw error;\n        }\n    }\n    /**\n   * Get all personnel with pagination and filtering\n   * @param page - Page number (default: 1)\n   * @param limit - Items per page (default: 10)\n   * @param options - Additional query options\n   * @returns Paginated personnel data\n   */ static async getAllPersonnel(page = 1, limit = 10, options) {\n        try {\n            return await _supabaseService__WEBPACK_IMPORTED_MODULE_0__.SupabaseService.getAllPersonnel({\n                page,\n                limit,\n                ...options\n            });\n        } catch (error) {\n            console.error('[DatabaseService] Error in getAllPersonnel:', error);\n            throw error;\n        }\n    }\n    // =====================================================\n    // PERSONNEL DOCUMENTS OPERATIONS\n    // =====================================================\n    /**\n   * Get all documents for a personnel\n   * @param personnelId - Personnel ID\n   * @returns Array of personnel documents\n   */ static async getPersonnelDocuments(personnelId) {\n        try {\n            return await _supabaseService__WEBPACK_IMPORTED_MODULE_0__.SupabaseService.getPersonnelDocuments(personnelId);\n        } catch (error) {\n            console.error('[DatabaseService] Error in getPersonnelDocuments:', error);\n            throw error;\n        }\n    }\n    /**\n   * Create a new personnel document\n   * @param documentData - Document data to insert (without id, created_at, updated_at)\n   * @returns Created document object\n   */ static async createPersonnelDocument(documentData) {\n        try {\n            return await _supabaseService__WEBPACK_IMPORTED_MODULE_0__.SupabaseService.createPersonnelDocument(documentData);\n        } catch (error) {\n            console.error('[DatabaseService] Error in createPersonnelDocument:', error);\n            throw error;\n        }\n    }\n    // =====================================================\n    // UTILITY OPERATIONS\n    // =====================================================\n    /**\n   * Database health check\n   * @returns Health check result\n   */ static async healthCheck() {\n        try {\n            return await _supabaseService__WEBPACK_IMPORTED_MODULE_0__.SupabaseService.healthCheck();\n        } catch (error) {\n            console.error('[DatabaseService] Error in healthCheck:', error);\n            return {\n                success: false,\n                message: `Health check failed: ${error instanceof Error ? error.message : 'Unknown error'}`,\n                details: {\n                    error\n                }\n            };\n        }\n    }\n}\n// Export as default for easy importing\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DatabaseService);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db.ts":
/*!***********************!*\
  !*** ./src/lib/db.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _database__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./database */ \"(rsc)/./src/lib/database.ts\");\n/**\n * Enterprise Database Connection\n * This file provides the main database interface for the application.\n * It now uses Supabase instead of mock data for production-ready functionality.\n */ \n// Export the enterprise database service as the main database interface\nconst db = _database__WEBPACK_IMPORTED_MODULE_0__.DatabaseService;\n// For backward compatibility and easy migration, we also export it as default\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_database__WEBPACK_IMPORTED_MODULE_0__.DatabaseService);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2RiLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBOzs7O0NBSUMsR0FFMkM7QUFFNUMsd0VBQXdFO0FBQ2pFLE1BQU1DLEtBQUtELHNEQUFlQSxDQUFBO0FBRWpDLDhFQUE4RTtBQUM5RSxpRUFBZUEsc0RBQWVBLEVBQUEiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcREFCQklFXFxPbmVEcml2ZVxcRGVza3RvcFxcbGd1LXByb2plY3QtYXBwXFxzcmNcXGxpYlxcZGIudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBFbnRlcnByaXNlIERhdGFiYXNlIENvbm5lY3Rpb25cbiAqIFRoaXMgZmlsZSBwcm92aWRlcyB0aGUgbWFpbiBkYXRhYmFzZSBpbnRlcmZhY2UgZm9yIHRoZSBhcHBsaWNhdGlvbi5cbiAqIEl0IG5vdyB1c2VzIFN1cGFiYXNlIGluc3RlYWQgb2YgbW9jayBkYXRhIGZvciBwcm9kdWN0aW9uLXJlYWR5IGZ1bmN0aW9uYWxpdHkuXG4gKi9cblxuaW1wb3J0IHsgRGF0YWJhc2VTZXJ2aWNlIH0gZnJvbSAnLi9kYXRhYmFzZSdcblxuLy8gRXhwb3J0IHRoZSBlbnRlcnByaXNlIGRhdGFiYXNlIHNlcnZpY2UgYXMgdGhlIG1haW4gZGF0YWJhc2UgaW50ZXJmYWNlXG5leHBvcnQgY29uc3QgZGIgPSBEYXRhYmFzZVNlcnZpY2VcblxuLy8gRm9yIGJhY2t3YXJkIGNvbXBhdGliaWxpdHkgYW5kIGVhc3kgbWlncmF0aW9uLCB3ZSBhbHNvIGV4cG9ydCBpdCBhcyBkZWZhdWx0XG5leHBvcnQgZGVmYXVsdCBEYXRhYmFzZVNlcnZpY2VcbiJdLCJuYW1lcyI6WyJEYXRhYmFzZVNlcnZpY2UiLCJkYiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getSupabaseServerClient: () => (/* binding */ getSupabaseServerClient),\n/* harmony export */   supabase: () => (/* binding */ supabase),\n/* harmony export */   supabaseAdmin: () => (/* binding */ supabaseAdmin),\n/* harmony export */   testSupabaseConnection: () => (/* binding */ testSupabaseConnection)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/**\n * DEPRECATED: This file is being replaced by the new Supabase Auth utilities\n * Please use utils/supabase/client.ts and utils/supabase/server.ts instead\n *\n * This file is kept for backward compatibility with existing database operations\n * but should not be used for new authentication features.\n */ \n// Get environment variables with fallbacks for development\nconst supabaseUrl = \"https://lkolpgpmdculqqfqyzaf.supabase.co\" || 0;\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imxrb2xwZ3BtZGN1bHFxZnF5emFmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk0ODE5ODcsImV4cCI6MjA2NTA1Nzk4N30.MRwyyo6wLKs2HWa4tQdfBPEq3mDee19lckU3MnVyWhU\" || 0;\nconst supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imxrb2xwZ3BtZGN1bHFxZnF5emFmIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0OTQ4MTk4NywiZXhwIjoyMDY1MDU3OTg3fQ.k4XWhiD9cbiJxN7s4ZPQYYCZAcXbLw8xIc3k4R4ROv0';\n// Validate required environment variables\nif (!supabaseUrl || !supabaseAnonKey) {\n    console.error('Missing Supabase environment variables:', {\n        url: !!supabaseUrl,\n        anonKey: !!supabaseAnonKey\n    });\n}\n// Legacy client for backward compatibility (database operations only)\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey, {\n    auth: {\n        autoRefreshToken: true,\n        persistSession: true,\n        detectSessionInUrl: true\n    }\n});\n// Admin client for server-side operations (bypasses RLS)\nconst supabaseAdmin = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseServiceKey || supabaseAnonKey, {\n    auth: {\n        autoRefreshToken: false,\n        persistSession: false\n    }\n});\n// Helper function to get server-side client (legacy)\nconst getSupabaseServerClient = ()=>{\n    return (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\n};\n// Connection test function\nconst testSupabaseConnection = async ()=>{\n    try {\n        const { error } = await supabase.from('users').select('count').limit(1);\n        if (error && error.code !== 'PGRST116') {\n            throw error;\n        }\n        return {\n            success: true,\n            message: 'Connected to Supabase successfully!'\n        };\n    } catch (error) {\n        console.error('Supabase connection test failed:', error);\n        return {\n            success: false,\n            message: `Connection failed: ${error instanceof Error ? error.message : 'Unknown error'}`\n        };\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3N1cGFiYXNlLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQUE7Ozs7OztDQU1DLEdBRW1EO0FBR3BELDJEQUEyRDtBQUMzRCxNQUFNQyxjQUFjQywwQ0FBb0MsSUFBSSxDQUEwQztBQUN0RyxNQUFNRyxrQkFBa0JILGtOQUF5QyxJQUFJLENBQWtOO0FBQ3ZSLE1BQU1LLHFCQUFxQkwsUUFBUUMsR0FBRyxDQUFDSyx5QkFBeUIsSUFBSTtBQUVwRSwwQ0FBMEM7QUFDMUMsSUFBSSxDQUFDUCxlQUFlLENBQUNJLGlCQUFpQjtJQUNwQ0ksUUFBUUMsS0FBSyxDQUFDLDJDQUEyQztRQUN2REMsS0FBSyxDQUFDLENBQUNWO1FBQ1BXLFNBQVMsQ0FBQyxDQUFDUDtJQUNiO0FBQ0Y7QUFFQSxzRUFBc0U7QUFDL0QsTUFBTVEsV0FBV2IsbUVBQVlBLENBQVdDLGFBQWFJLGlCQUFpQjtJQUMzRVMsTUFBTTtRQUNKQyxrQkFBa0I7UUFDbEJDLGdCQUFnQjtRQUNoQkMsb0JBQW9CO0lBQ3RCO0FBQ0YsR0FBRTtBQUVGLHlEQUF5RDtBQUNsRCxNQUFNQyxnQkFBZ0JsQixtRUFBWUEsQ0FBV0MsYUFBYU0sc0JBQXNCRixpQkFBaUI7SUFDdEdTLE1BQU07UUFDSkMsa0JBQWtCO1FBQ2xCQyxnQkFBZ0I7SUFDbEI7QUFDRixHQUFFO0FBRUYscURBQXFEO0FBQzlDLE1BQU1HLDBCQUEwQjtJQUNyQyxPQUFPbkIsbUVBQVlBLENBQVdDLGFBQWFJO0FBQzdDLEVBQUM7QUFFRCwyQkFBMkI7QUFDcEIsTUFBTWUseUJBQXlCO0lBQ3BDLElBQUk7UUFDRixNQUFNLEVBQUVWLEtBQUssRUFBRSxHQUFHLE1BQU1HLFNBQ3JCUSxJQUFJLENBQUMsU0FDTEMsTUFBTSxDQUFDLFNBQ1BDLEtBQUssQ0FBQztRQUVULElBQUliLFNBQVNBLE1BQU1jLElBQUksS0FBSyxZQUFZO1lBQ3RDLE1BQU1kO1FBQ1I7UUFFQSxPQUFPO1lBQUVlLFNBQVM7WUFBTUMsU0FBUztRQUFzQztJQUN6RSxFQUFFLE9BQU9oQixPQUFPO1FBQ2RELFFBQVFDLEtBQUssQ0FBQyxvQ0FBb0NBO1FBQ2xELE9BQU87WUFDTGUsU0FBUztZQUNUQyxTQUFTLENBQUMsbUJBQW1CLEVBQUVoQixpQkFBaUJpQixRQUFRakIsTUFBTWdCLE9BQU8sR0FBRyxpQkFBaUI7UUFDM0Y7SUFDRjtBQUNGLEVBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcREFCQklFXFxPbmVEcml2ZVxcRGVza3RvcFxcbGd1LXByb2plY3QtYXBwXFxzcmNcXGxpYlxcc3VwYWJhc2UudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBERVBSRUNBVEVEOiBUaGlzIGZpbGUgaXMgYmVpbmcgcmVwbGFjZWQgYnkgdGhlIG5ldyBTdXBhYmFzZSBBdXRoIHV0aWxpdGllc1xuICogUGxlYXNlIHVzZSB1dGlscy9zdXBhYmFzZS9jbGllbnQudHMgYW5kIHV0aWxzL3N1cGFiYXNlL3NlcnZlci50cyBpbnN0ZWFkXG4gKlxuICogVGhpcyBmaWxlIGlzIGtlcHQgZm9yIGJhY2t3YXJkIGNvbXBhdGliaWxpdHkgd2l0aCBleGlzdGluZyBkYXRhYmFzZSBvcGVyYXRpb25zXG4gKiBidXQgc2hvdWxkIG5vdCBiZSB1c2VkIGZvciBuZXcgYXV0aGVudGljYXRpb24gZmVhdHVyZXMuXG4gKi9cblxuaW1wb3J0IHsgY3JlYXRlQ2xpZW50IH0gZnJvbSAnQHN1cGFiYXNlL3N1cGFiYXNlLWpzJ1xuaW1wb3J0IHsgRGF0YWJhc2UgfSBmcm9tICcuL2RhdGFiYXNlLnR5cGVzJ1xuXG4vLyBHZXQgZW52aXJvbm1lbnQgdmFyaWFibGVzIHdpdGggZmFsbGJhY2tzIGZvciBkZXZlbG9wbWVudFxuY29uc3Qgc3VwYWJhc2VVcmwgPSBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19TVVBBQkFTRV9VUkwgfHwgJ2h0dHBzOi8vbGtvbHBncG1kY3VscXFmcXl6YWYuc3VwYWJhc2UuY28nXG5jb25zdCBzdXBhYmFzZUFub25LZXkgPSBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19TVVBBQkFTRV9BTk9OX0tFWSB8fCAnZXlKaGJHY2lPaUpJVXpJMU5pSXNJblI1Y0NJNklrcFhWQ0o5LmV5SnBjM01pT2lKemRYQmhZbUZ6WlNJc0luSmxaaUk2SW14cmIyeHdaM0J0WkdOMWJIRnhabkY1ZW1GbUlpd2ljbTlzWlNJNkltRnViMjRpTENKcFlYUWlPakUzTkRrME9ERTVPRGNzSW1WNGNDSTZNakEyTlRBMU56azROMzAuTVJ3eXlvNndMS3MySFdhNHRRZGZCUEVxM21EZWUxOWxja1UzTW5WeVdoVSdcbmNvbnN0IHN1cGFiYXNlU2VydmljZUtleSA9IHByb2Nlc3MuZW52LlNVUEFCQVNFX1NFUlZJQ0VfUk9MRV9LRVkgfHwgJ2V5SmhiR2NpT2lKSVV6STFOaUlzSW5SNWNDSTZJa3BYVkNKOS5leUpwYzNNaU9pSnpkWEJoWW1GelpTSXNJbkpsWmlJNklteHJiMnh3WjNCdFpHTjFiSEZ4Wm5GNWVtRm1JaXdpY205c1pTSTZJbk5sY25acFkyVmZjbTlzWlNJc0ltbGhkQ0k2TVRjME9UUTRNVGs0Tnl3aVpYaHdJam95TURZMU1EVTNPVGczZlEuazRYV2hpRDljYmlKeE43czRaUFFZWUNaQWNYYkx3OHhJYzNrNFI0Uk92MCdcblxuLy8gVmFsaWRhdGUgcmVxdWlyZWQgZW52aXJvbm1lbnQgdmFyaWFibGVzXG5pZiAoIXN1cGFiYXNlVXJsIHx8ICFzdXBhYmFzZUFub25LZXkpIHtcbiAgY29uc29sZS5lcnJvcignTWlzc2luZyBTdXBhYmFzZSBlbnZpcm9ubWVudCB2YXJpYWJsZXM6Jywge1xuICAgIHVybDogISFzdXBhYmFzZVVybCxcbiAgICBhbm9uS2V5OiAhIXN1cGFiYXNlQW5vbktleVxuICB9KVxufVxuXG4vLyBMZWdhY3kgY2xpZW50IGZvciBiYWNrd2FyZCBjb21wYXRpYmlsaXR5IChkYXRhYmFzZSBvcGVyYXRpb25zIG9ubHkpXG5leHBvcnQgY29uc3Qgc3VwYWJhc2UgPSBjcmVhdGVDbGllbnQ8RGF0YWJhc2U+KHN1cGFiYXNlVXJsLCBzdXBhYmFzZUFub25LZXksIHtcbiAgYXV0aDoge1xuICAgIGF1dG9SZWZyZXNoVG9rZW46IHRydWUsXG4gICAgcGVyc2lzdFNlc3Npb246IHRydWUsXG4gICAgZGV0ZWN0U2Vzc2lvbkluVXJsOiB0cnVlXG4gIH1cbn0pXG5cbi8vIEFkbWluIGNsaWVudCBmb3Igc2VydmVyLXNpZGUgb3BlcmF0aW9ucyAoYnlwYXNzZXMgUkxTKVxuZXhwb3J0IGNvbnN0IHN1cGFiYXNlQWRtaW4gPSBjcmVhdGVDbGllbnQ8RGF0YWJhc2U+KHN1cGFiYXNlVXJsLCBzdXBhYmFzZVNlcnZpY2VLZXkgfHwgc3VwYWJhc2VBbm9uS2V5LCB7XG4gIGF1dGg6IHtcbiAgICBhdXRvUmVmcmVzaFRva2VuOiBmYWxzZSxcbiAgICBwZXJzaXN0U2Vzc2lvbjogZmFsc2VcbiAgfVxufSlcblxuLy8gSGVscGVyIGZ1bmN0aW9uIHRvIGdldCBzZXJ2ZXItc2lkZSBjbGllbnQgKGxlZ2FjeSlcbmV4cG9ydCBjb25zdCBnZXRTdXBhYmFzZVNlcnZlckNsaWVudCA9ICgpID0+IHtcbiAgcmV0dXJuIGNyZWF0ZUNsaWVudDxEYXRhYmFzZT4oc3VwYWJhc2VVcmwsIHN1cGFiYXNlQW5vbktleSlcbn1cblxuLy8gQ29ubmVjdGlvbiB0ZXN0IGZ1bmN0aW9uXG5leHBvcnQgY29uc3QgdGVzdFN1cGFiYXNlQ29ubmVjdGlvbiA9IGFzeW5jICgpID0+IHtcbiAgdHJ5IHtcbiAgICBjb25zdCB7IGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgLmZyb20oJ3VzZXJzJylcbiAgICAgIC5zZWxlY3QoJ2NvdW50JylcbiAgICAgIC5saW1pdCgxKVxuICAgIFxuICAgIGlmIChlcnJvciAmJiBlcnJvci5jb2RlICE9PSAnUEdSU1QxMTYnKSB7IC8vIFBHUlNUMTE2ID0gcmVsYXRpb24gZG9lcyBub3QgZXhpc3QgKGV4cGVjdGVkIGlmIG5vIHRhYmxlcylcbiAgICAgIHRocm93IGVycm9yXG4gICAgfVxuICAgIFxuICAgIHJldHVybiB7IHN1Y2Nlc3M6IHRydWUsIG1lc3NhZ2U6ICdDb25uZWN0ZWQgdG8gU3VwYWJhc2Ugc3VjY2Vzc2Z1bGx5IScgfVxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ1N1cGFiYXNlIGNvbm5lY3Rpb24gdGVzdCBmYWlsZWQ6JywgZXJyb3IpXG4gICAgcmV0dXJuIHsgXG4gICAgICBzdWNjZXNzOiBmYWxzZSwgXG4gICAgICBtZXNzYWdlOiBgQ29ubmVjdGlvbiBmYWlsZWQ6ICR7ZXJyb3IgaW5zdGFuY2VvZiBFcnJvciA/IGVycm9yLm1lc3NhZ2UgOiAnVW5rbm93biBlcnJvcid9YCBcbiAgICB9XG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJjcmVhdGVDbGllbnQiLCJzdXBhYmFzZVVybCIsInByb2Nlc3MiLCJlbnYiLCJORVhUX1BVQkxJQ19TVVBBQkFTRV9VUkwiLCJzdXBhYmFzZUFub25LZXkiLCJORVhUX1BVQkxJQ19TVVBBQkFTRV9BTk9OX0tFWSIsInN1cGFiYXNlU2VydmljZUtleSIsIlNVUEFCQVNFX1NFUlZJQ0VfUk9MRV9LRVkiLCJjb25zb2xlIiwiZXJyb3IiLCJ1cmwiLCJhbm9uS2V5Iiwic3VwYWJhc2UiLCJhdXRoIiwiYXV0b1JlZnJlc2hUb2tlbiIsInBlcnNpc3RTZXNzaW9uIiwiZGV0ZWN0U2Vzc2lvbkluVXJsIiwic3VwYWJhc2VBZG1pbiIsImdldFN1cGFiYXNlU2VydmVyQ2xpZW50IiwidGVzdFN1cGFiYXNlQ29ubmVjdGlvbiIsImZyb20iLCJzZWxlY3QiLCJsaW1pdCIsImNvZGUiLCJzdWNjZXNzIiwibWVzc2FnZSIsIkVycm9yIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabaseService.ts":
/*!************************************!*\
  !*** ./src/lib/supabaseService.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SupabaseService: () => (/* binding */ SupabaseService)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(rsc)/./src/lib/supabase.ts\");\n\n/**\n * Enterprise-grade Supabase Service Layer\n * Provides robust database operations with proper error handling,\n * logging, and performance optimization for production use.\n */ class SupabaseService {\n    // =====================================================\n    // USER OPERATIONS\n    // =====================================================\n    /**\n   * Find user by email address\n   * @param email - User's email address\n   * @returns User object or null if not found\n   */ static async findUserByEmail(email) {\n        try {\n            console.log(`[SupabaseService] Finding user by email: ${email}`);\n            const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from('users').select('*').eq('email', email).single();\n            if (error) {\n                if (error.code === 'PGRST116') {\n                    console.warn('[SupabaseService] Users table does not exist yet');\n                    return null;\n                }\n                console.error('[SupabaseService] Error finding user by email:', error);\n                throw new Error(`Database error: ${error.message}`);\n            }\n            console.log(`[SupabaseService] User found: ${data?.name || 'Unknown'}`);\n            return data;\n        } catch (error) {\n            console.error('[SupabaseService] Error in findUserByEmail:', error);\n            throw error;\n        }\n    }\n    /**\n   * Find user by ID\n   * @param id - User's ID\n   * @returns User object or null if not found\n   */ static async findUserById(id) {\n        try {\n            console.log(`[SupabaseService] Finding user by ID: ${id}`);\n            const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from('users').select('*').eq('id', id).single();\n            if (error) {\n                if (error.code === 'PGRST116') {\n                    console.warn('[SupabaseService] No user found with ID:', id);\n                    return null;\n                }\n                console.error('[SupabaseService] Error finding user by ID:', error);\n                throw new Error(`Database error: ${error.message}`);\n            }\n            return data;\n        } catch (error) {\n            console.error('[SupabaseService] Error in findUserById:', error);\n            throw error;\n        }\n    }\n    /**\n   * Create a new user\n   * @param userData - User data to insert\n   * @returns Created user object\n   */ static async createUser(userData) {\n        try {\n            console.log(`[SupabaseService] Creating user: ${userData.email}`);\n            const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from('users').insert({\n                ...userData,\n                created_at: new Date().toISOString(),\n                updated_at: new Date().toISOString()\n            }).select().single();\n            if (error) {\n                console.error('[SupabaseService] Error creating user:', error);\n                throw new Error(`Database error: ${error.message}`);\n            }\n            console.log(`[SupabaseService] User created successfully: ${data.name}`);\n            return data;\n        } catch (error) {\n            console.error('[SupabaseService] Error in createUser:', error);\n            throw error;\n        }\n    }\n    /**\n   * Update user data\n   * @param id - User ID to update\n   * @param userData - Partial user data to update\n   * @returns Updated user object or null if not found\n   */ static async updateUser(id, userData) {\n        try {\n            console.log(`[SupabaseService] Updating user ID: ${id}`);\n            const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from('users').update({\n                ...userData,\n                updated_at: new Date().toISOString()\n            }).eq('id', id).select().single();\n            if (error) {\n                console.error('[SupabaseService] Error updating user:', error);\n                throw new Error(`Database error: ${error.message}`);\n            }\n            console.log(`[SupabaseService] User updated successfully: ${data?.name || 'Unknown'}`);\n            return data;\n        } catch (error) {\n            console.error('[SupabaseService] Error in updateUser:', error);\n            throw error;\n        }\n    }\n    /**\n   * Delete user by ID\n   * @param id - User ID to delete\n   * @returns True if deleted successfully\n   */ static async deleteUser(id) {\n        try {\n            console.log(`[SupabaseService] Deleting user ID: ${id}`);\n            const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from('users').delete().eq('id', id);\n            if (error) {\n                console.error('[SupabaseService] Error deleting user:', error);\n                throw new Error(`Database error: ${error.message}`);\n            }\n            console.log(`[SupabaseService] User deleted successfully: ${id}`);\n            return true;\n        } catch (error) {\n            console.error('[SupabaseService] Error in deleteUser:', error);\n            throw error;\n        }\n    }\n    /**\n   * Get all users with optional filtering and pagination\n   * @param options - Query options\n   * @returns Array of users\n   */ static async getAllUsers(options) {\n        try {\n            console.log('[SupabaseService] Getting all users with options:', options);\n            let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from('users').select('*');\n            // Apply search filter\n            if (options?.search) {\n                query = query.or(`name.ilike.%${options.search}%,email.ilike.%${options.search}%`);\n            }\n            // Apply status filter\n            if (options?.status) {\n                query = query.eq('status', options.status);\n            }\n            // Apply pagination\n            if (options?.page && options?.limit) {\n                const from = (options.page - 1) * options.limit;\n                const to = from + options.limit - 1;\n                query = query.range(from, to);\n            }\n            // Order by name alphabetically (A-Z)\n            query = query.order('name', {\n                ascending: true\n            });\n            const { data, error } = await query;\n            if (error) {\n                console.error('[SupabaseService] Error getting all users:', error);\n                throw new Error(`Database error: ${error.message}`);\n            }\n            console.log(`[SupabaseService] Retrieved ${data?.length || 0} users`);\n            return data || [];\n        } catch (error) {\n            console.error('[SupabaseService] Error in getAllUsers:', error);\n            throw error;\n        }\n    }\n    // =====================================================\n    // PERSONNEL OPERATIONS\n    // =====================================================\n    /**\n   * Find personnel by email address\n   * @param email - Personnel's email address\n   * @returns Personnel object or null if not found\n   */ static async findPersonnelByEmail(email) {\n        try {\n            console.log(`[SupabaseService] Finding personnel by email: ${email}`);\n            const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from('personnel').select('*').eq('email', email).single();\n            if (error) {\n                if (error.code === 'PGRST116') {\n                    console.warn('[SupabaseService] Personnel table does not exist yet');\n                    return null;\n                }\n                console.error('[SupabaseService] Error finding personnel by email:', error);\n                throw new Error(`Database error: ${error.message}`);\n            }\n            console.log(`[SupabaseService] Personnel found: ${data?.name || 'Unknown'}`);\n            return data;\n        } catch (error) {\n            console.error('[SupabaseService] Error in findPersonnelByEmail:', error);\n            throw error;\n        }\n    }\n    /**\n   * Find personnel by ID\n   * @param id - Personnel's ID\n   * @returns Personnel object or null if not found\n   */ static async findPersonnelById(id) {\n        try {\n            console.log(`[SupabaseService] Finding personnel by ID: ${id}`);\n            const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from('personnel').select('*').eq('id', id).single();\n            if (error) {\n                if (error.code === 'PGRST116') {\n                    console.warn('[SupabaseService] No personnel found with ID:', id);\n                    return null;\n                }\n                console.error('[SupabaseService] Error finding personnel by ID:', error);\n                throw new Error(`Database error: ${error.message}`);\n            }\n            return data;\n        } catch (error) {\n            console.error('[SupabaseService] Error in findPersonnelById:', error);\n            throw error;\n        }\n    }\n    /**\n   * Create new personnel record\n   * @param personnelData - Personnel data to insert\n   * @returns Created personnel object\n   */ static async createPersonnel(personnelData) {\n        try {\n            console.log(`[SupabaseService] Creating personnel: ${personnelData.email}`);\n            const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from('personnel').insert({\n                ...personnelData,\n                created_at: new Date().toISOString(),\n                updated_at: new Date().toISOString()\n            }).select().single();\n            if (error) {\n                console.error('[SupabaseService] Error creating personnel:', error);\n                throw new Error(`Database error: ${error.message}`);\n            }\n            console.log(`[SupabaseService] Personnel created successfully: ${data.name}`);\n            return data;\n        } catch (error) {\n            console.error('[SupabaseService] Error in createPersonnel:', error);\n            throw error;\n        }\n    }\n    /**\n   * Update personnel data\n   * @param id - Personnel ID to update\n   * @param personnelData - Partial personnel data to update\n   * @returns Updated personnel object or null if not found\n   */ static async updatePersonnel(id, personnelData) {\n        try {\n            console.log(`[SupabaseService] Updating personnel ID: ${id}`);\n            const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from('personnel').update({\n                ...personnelData,\n                updated_at: new Date().toISOString()\n            }).eq('id', id).select().single();\n            if (error) {\n                console.error('[SupabaseService] Error updating personnel:', error);\n                throw new Error(`Database error: ${error.message}`);\n            }\n            console.log(`[SupabaseService] Personnel updated successfully: ${data?.name || 'Unknown'}`);\n            return data;\n        } catch (error) {\n            console.error('[SupabaseService] Error in updatePersonnel:', error);\n            throw error;\n        }\n    }\n    /**\n   * Delete personnel by ID\n   * @param id - Personnel ID to delete\n   * @returns True if deleted successfully\n   */ static async deletePersonnel(id) {\n        try {\n            console.log(`[SupabaseService] Deleting personnel ID: ${id}`);\n            const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from('personnel').delete().eq('id', id);\n            if (error) {\n                console.error('[SupabaseService] Error deleting personnel:', error);\n                throw new Error(`Database error: ${error.message}`);\n            }\n            console.log(`[SupabaseService] Personnel deleted successfully: ${id}`);\n            return true;\n        } catch (error) {\n            console.error('[SupabaseService] Error in deletePersonnel:', error);\n            throw error;\n        }\n    }\n    /**\n   * Get all personnel with pagination and filtering\n   * @param options - Query options\n   * @returns Paginated personnel data\n   */ static async getAllPersonnel(options) {\n        try {\n            const page = options?.page || 1;\n            const limit = options?.limit || 10;\n            console.log(`[SupabaseService] Getting all personnel - Page: ${page}, Limit: ${limit}`);\n            // Build query for count\n            let countQuery = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from('personnel').select('*', {\n                count: 'exact',\n                head: true\n            });\n            // Build query for data\n            let dataQuery = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from('personnel').select('*');\n            // Apply filters to both queries\n            if (options?.search) {\n                const searchFilter = `name.ilike.%${options.search}%,email.ilike.%${options.search}%`;\n                countQuery = countQuery.or(searchFilter);\n                dataQuery = dataQuery.or(searchFilter);\n            }\n            if (options?.department) {\n                countQuery = countQuery.eq('department', options.department);\n                dataQuery = dataQuery.eq('department', options.department);\n            }\n            if (options?.status) {\n                countQuery = countQuery.eq('status', options.status);\n                dataQuery = dataQuery.eq('status', options.status);\n            }\n            // Get total count\n            const { count, error: countError } = await countQuery;\n            if (countError) {\n                console.error('[SupabaseService] Error getting personnel count:', countError);\n                throw new Error(`Database error: ${countError.message}`);\n            }\n            // Apply pagination and ordering to data query\n            const from = (page - 1) * limit;\n            const to = from + limit - 1;\n            // Apply sorting based on sort parameter\n            const sortBy = options?.sort || 'name_asc';\n            let sortedQuery = dataQuery.range(from, to);\n            switch(sortBy){\n                case 'id_asc':\n                    sortedQuery = sortedQuery.order('id', {\n                        ascending: true\n                    });\n                    break;\n                case 'id_desc':\n                    sortedQuery = sortedQuery.order('id', {\n                        ascending: false\n                    });\n                    break;\n                case 'name_desc':\n                    sortedQuery = sortedQuery.order('name', {\n                        ascending: false\n                    });\n                    break;\n                case 'name_asc':\n                default:\n                    sortedQuery = sortedQuery.order('name', {\n                        ascending: true\n                    });\n                    break;\n            }\n            const { data, error } = await sortedQuery;\n            if (error) {\n                console.error('[SupabaseService] Error getting personnel data:', error);\n                throw new Error(`Database error: ${error.message}`);\n            }\n            const result = {\n                data: data || [],\n                pagination: {\n                    page,\n                    limit,\n                    total: count || 0,\n                    pages: Math.ceil((count || 0) / limit)\n                }\n            };\n            console.log(`[SupabaseService] Retrieved ${result.data.length} personnel records`);\n            return result;\n        } catch (error) {\n            console.error('[SupabaseService] Error in getAllPersonnel:', error);\n            throw error;\n        }\n    }\n    // =====================================================\n    // PERSONNEL DOCUMENTS OPERATIONS\n    // =====================================================\n    /**\n   * Get all documents for a personnel\n   * @param personnelId - Personnel ID\n   * @returns Array of personnel documents\n   */ static async getPersonnelDocuments(personnelId) {\n        try {\n            console.log(`[SupabaseService] Getting documents for personnel ID: ${personnelId}`);\n            const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from('personnel_documents').select('*').eq('personnel_id', personnelId).order('created_at', {\n                ascending: false\n            });\n            if (error) {\n                console.error('[SupabaseService] Error getting personnel documents:', error);\n                throw new Error(`Database error: ${error.message}`);\n            }\n            console.log(`[SupabaseService] Retrieved ${data?.length || 0} documents`);\n            return data || [];\n        } catch (error) {\n            console.error('[SupabaseService] Error in getPersonnelDocuments:', error);\n            throw error;\n        }\n    }\n    /**\n   * Create a new personnel document\n   * @param documentData - Document data to insert\n   * @returns Created document object\n   */ static async createPersonnelDocument(documentData) {\n        try {\n            console.log(`[SupabaseService] Creating document: ${documentData.filename}`);\n            const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from('personnel_documents').insert({\n                ...documentData,\n                created_at: new Date().toISOString(),\n                updated_at: new Date().toISOString()\n            }).select().single();\n            if (error) {\n                console.error('[SupabaseService] Error creating personnel document:', error);\n                throw new Error(`Database error: ${error.message}`);\n            }\n            console.log(`[SupabaseService] Document created successfully: ${data.filename}`);\n            return data;\n        } catch (error) {\n            console.error('[SupabaseService] Error in createPersonnelDocument:', error);\n            throw error;\n        }\n    }\n    // =====================================================\n    // UTILITY OPERATIONS\n    // =====================================================\n    /**\n   * Database health check\n   * @returns Health check result\n   */ static async healthCheck() {\n        try {\n            console.log('[SupabaseService] Performing health check');\n            // Test basic connection\n            const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from('users').select('count').limit(1);\n            if (error && error.code !== 'PGRST116') {\n                throw error;\n            }\n            const result = {\n                success: true,\n                message: 'Supabase connection successful!',\n                details: {\n                    connected: true,\n                    tablesExist: error?.code !== 'PGRST116',\n                    timestamp: new Date().toISOString()\n                }\n            };\n            console.log('[SupabaseService] Health check passed');\n            return result;\n        } catch (error) {\n            const result = {\n                success: false,\n                message: `Connection failed: ${error instanceof Error ? error.message : 'Unknown error'}`,\n                details: {\n                    error\n                }\n            };\n            console.error('[SupabaseService] Health check failed:', result);\n            return result;\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabaseService.ts\n");

/***/ }),

/***/ "(rsc)/./src/utils/supabase/server.ts":
/*!**************************************!*\
  !*** ./src/utils/supabase/server.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createAdminClient: () => (/* binding */ createAdminClient),\n/* harmony export */   createClient: () => (/* binding */ createClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/**\n * Supabase Client for Server Components\n * \n * This client is used in Server Components, Server Actions, and Route Handlers.\n * It properly handles cookies and session management on the server side.\n * \n * Usage:\n * ```typescript\n * import { createClient } from '@/utils/supabase/server'\n * \n * const supabase = await createClient()\n * const { data: { user } } = await supabase.auth.getUser()\n * ```\n */ \n\nasync function createClient() {\n    const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://lkolpgpmdculqqfqyzaf.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imxrb2xwZ3BtZGN1bHFxZnF5emFmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk0ODE5ODcsImV4cCI6MjA2NTA1Nzk4N30.MRwyyo6wLKs2HWa4tQdfBPEq3mDee19lckU3MnVyWhU\", {\n        cookies: {\n            getAll () {\n                return cookieStore.getAll();\n            },\n            setAll (cookiesToSet) {\n                try {\n                    cookiesToSet.forEach(({ name, value, options })=>cookieStore.set(name, value, options));\n                } catch  {\n                // The `setAll` method was called from a Server Component.\n                // This can be ignored if you have middleware refreshing\n                // user sessions.\n                }\n            }\n        }\n    });\n}\n// Service role client for admin operations (bypasses RLS)\nasync function createAdminClient() {\n    const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://lkolpgpmdculqqfqyzaf.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY, {\n        cookies: {\n            getAll () {\n                return cookieStore.getAll();\n            },\n            setAll (cookiesToSet) {\n                try {\n                    cookiesToSet.forEach(({ name, value, options })=>cookieStore.set(name, value, options));\n                } catch  {\n                // Ignore cookie setting errors in Server Components\n                }\n            }\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/utils/supabase/server.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/zod"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fpersonnel%2Froute&page=%2Fapi%2Fpersonnel%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fpersonnel%2Froute.ts&appDir=C%3A%5CUsers%5CDABBIE%5COneDrive%5CDesktop%5Clgu-project-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDABBIE%5COneDrive%5CDesktop%5Clgu-project-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();