/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/cloudinary/media/route";
exports.ids = ["app/api/cloudinary/media/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcloudinary%2Fmedia%2Froute&page=%2Fapi%2Fcloudinary%2Fmedia%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcloudinary%2Fmedia%2Froute.ts&appDir=C%3A%5CUsers%5CDABBIE%5COneDrive%5CDesktop%5Clgu-project-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDABBIE%5COneDrive%5CDesktop%5Clgu-project-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcloudinary%2Fmedia%2Froute&page=%2Fapi%2Fcloudinary%2Fmedia%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcloudinary%2Fmedia%2Froute.ts&appDir=C%3A%5CUsers%5CDABBIE%5COneDrive%5CDesktop%5Clgu-project-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDABBIE%5COneDrive%5CDesktop%5Clgu-project-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_DABBIE_OneDrive_Desktop_lgu_project_app_src_app_api_cloudinary_media_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/cloudinary/media/route.ts */ \"(rsc)/./src/app/api/cloudinary/media/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/cloudinary/media/route\",\n        pathname: \"/api/cloudinary/media\",\n        filename: \"route\",\n        bundlePath: \"app/api/cloudinary/media/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\app\\\\api\\\\cloudinary\\\\media\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_DABBIE_OneDrive_Desktop_lgu_project_app_src_app_api_cloudinary_media_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcloudinary%2Fmedia%2Froute&page=%2Fapi%2Fcloudinary%2Fmedia%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcloudinary%2Fmedia%2Froute.ts&appDir=C%3A%5CUsers%5CDABBIE%5COneDrive%5CDesktop%5Clgu-project-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDABBIE%5COneDrive%5CDesktop%5Clgu-project-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/cloudinary/media/route.ts":
/*!***********************************************!*\
  !*** ./src/app/api/cloudinary/media/route.ts ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabaseMediaService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabaseMediaService */ \"(rsc)/./src/lib/supabaseMediaService.ts\");\n/* harmony import */ var _lib_bidirectionalSyncService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/bidirectionalSyncService */ \"(rsc)/./src/lib/bidirectionalSyncService.ts\");\n/**\n * Cloudinary Media API Route\n *\n * Handles fetching media items from Supabase database with Cloudinary sync.\n * Provides fast, persistent access to media library with perfect mirroring.\n */ \n\n\n/**\n * GET /api/cloudinary/media\n * Fetch media items with enterprise-grade pagination and filtering\n */ async function GET(request) {\n    try {\n        console.log('[Cloudinary Media API] Fetching media items...');\n        // Parse query parameters\n        const { searchParams } = new URL(request.url);\n        const page = parseInt(searchParams.get('page') || '1');\n        const limit = parseInt(searchParams.get('limit') || '50');\n        const folder = searchParams.get('folder') || undefined;\n        const resourceType = searchParams.get('resource_type');\n        const search = searchParams.get('search') || undefined;\n        const tags = searchParams.get('tags')?.split(',').filter(Boolean) || undefined;\n        const sortBy = searchParams.get('sort_by')?.split(',') || undefined;\n        const sortOrder = searchParams.get('sort_order') || 'desc';\n        // First, check if database is properly set up\n        let databaseReady = false;\n        let result = null;\n        let stats = null;\n        try {\n            // Tables exist, so test actual functionality\n            databaseReady = true;\n            // If database is ready, fetch actual data\n            const dbResult = await _lib_supabaseMediaService__WEBPACK_IMPORTED_MODULE_1__.SupabaseMediaService.searchMediaAssets({\n                page,\n                limit,\n                folder,\n                resource_type: resourceType,\n                search,\n                tags,\n                sort_by: sortBy?.[0] || 'created_at',\n                sort_order: sortOrder\n            });\n            // Transform MediaAsset to expected format\n            result = {\n                assets: dbResult.assets.map((asset)=>({\n                        public_id: asset.cloudinary_public_id,\n                        secure_url: asset.secure_url,\n                        format: asset.format,\n                        bytes: asset.file_size,\n                        width: asset.width,\n                        height: asset.height,\n                        resource_type: asset.resource_type,\n                        created_at: asset.created_at,\n                        tags: asset.tags,\n                        [Symbol.iterator]: function*() {\n                            yield* Object.entries(this);\n                        }\n                    })),\n                total: dbResult.total,\n                page: dbResult.page,\n                limit: dbResult.limit,\n                has_next: dbResult.has_next,\n                has_prev: dbResult.has_prev\n            };\n            stats = await _lib_supabaseMediaService__WEBPACK_IMPORTED_MODULE_1__.SupabaseMediaService.getMediaStats();\n            console.log(`[Cloudinary Media API] Found ${result?.assets?.length || 0} items (page ${page}) from database`);\n        } catch (dbError) {\n            console.warn('[Cloudinary Media API] Database not ready:', dbError);\n            // Check if this is a table missing error\n            const errorMessage = dbError instanceof Error ? dbError.message : String(dbError);\n            const isTableMissing = errorMessage.includes('relation \"media_assets\" does not exist') || errorMessage.includes('table \"media_assets\" does not exist') || errorMessage.includes('does not exist') || errorMessage.includes('Failed to search media assets');\n            databaseReady = !isTableMissing;\n            // Fallback: Return empty result with setup instructions\n            result = {\n                assets: [],\n                total: 0,\n                page: 1,\n                limit: 50,\n                has_next: false,\n                has_prev: false\n            };\n            stats = {\n                total_assets: 0,\n                total_images: 0,\n                total_videos: 0,\n                total_size: 0,\n                synced_assets: 0,\n                pending_assets: 0,\n                error_assets: 0\n            };\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: databaseReady,\n            items: result?.assets || [],\n            pagination: {\n                page: result?.page || 1,\n                limit: result?.limit || 50,\n                total: result?.total || 0,\n                pages: Math.ceil((result?.total || 0) / (result?.limit || 50)),\n                has_next: result?.has_next || false,\n                has_prev: result?.has_prev || false\n            },\n            stats: {\n                total_images: stats?.total_images || 0,\n                total_videos: stats?.total_videos || 0,\n                total_files: stats?.total_assets || 0,\n                total_size: stats?.total_size || 0\n            },\n            sync_status: {\n                last_sync: new Date().toISOString(),\n                is_synced: databaseReady && (stats?.pending_assets || 0) === 0 && (stats?.error_assets || 0) === 0,\n                pending_operations: (stats?.pending_assets || 0) + (stats?.error_assets || 0),\n                synced_assets: stats?.synced_assets || 0,\n                error_assets: stats?.error_assets || 0,\n                database_ready: databaseReady\n            },\n            database_setup: {\n                ready: databaseReady,\n                message: databaseReady ? 'Database is properly configured' : 'Database setup required - run the SQL script from docs/full-complete-supabase-script.md',\n                setup_endpoint: '/api/setup-media-db',\n                script_location: 'docs/full-complete-supabase-script.md'\n            },\n            timestamp: new Date().toISOString()\n        });\n    } catch (error) {\n        console.error('[Cloudinary Media API] Fetch failed:', error);\n        // Return empty result with error information\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            items: [],\n            pagination: {\n                page: 1,\n                limit: 50,\n                total: 0,\n                pages: 0,\n                has_next: false,\n                has_prev: false\n            },\n            stats: {\n                total_images: 0,\n                total_videos: 0,\n                total_files: 0,\n                total_size: 0\n            },\n            sync_status: {\n                last_sync: new Date().toISOString(),\n                is_synced: false,\n                pending_operations: 0,\n                database_ready: false\n            },\n            database_setup: {\n                ready: false,\n                message: 'Database setup required - run the SQL script from docs/full-complete-supabase-script.md',\n                setup_endpoint: '/api/setup-media-db',\n                script_location: 'docs/full-complete-supabase-script.md'\n            },\n            error: error instanceof Error ? error.message : 'Unknown error',\n            timestamp: new Date().toISOString()\n        });\n    }\n}\n/**\n * DELETE /api/cloudinary/media\n * Delete media items with bidirectional sync\n */ async function DELETE(request) {\n    try {\n        console.log('[Cloudinary Media API] Processing delete request...');\n        // Parse query parameters for public IDs\n        const { searchParams } = new URL(request.url);\n        const publicIds = searchParams.get('public_ids')?.split(',').filter(Boolean) || [];\n        if (publicIds.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'No public IDs provided for deletion',\n                timestamp: new Date().toISOString()\n            }, {\n                status: 400\n            });\n        }\n        console.log(`[Cloudinary Media API] Deleting ${publicIds.length} items:`, publicIds);\n        const results = {\n            deleted: [],\n            failed: []\n        };\n        // Process each deletion\n        for (const publicId of publicIds){\n            try {\n                // Get asset info first\n                const asset = await _lib_supabaseMediaService__WEBPACK_IMPORTED_MODULE_1__.SupabaseMediaService.getMediaAssetByPublicId(publicId);\n                if (!asset) {\n                    results.failed.push({\n                        public_id: publicId,\n                        error: 'Asset not found in database'\n                    });\n                    continue;\n                }\n                // Soft delete in database (will be synced to Cloudinary)\n                await _lib_supabaseMediaService__WEBPACK_IMPORTED_MODULE_1__.SupabaseMediaService.softDeleteMediaAsset(publicId, 'admin');\n                // Mark as pending for Cloudinary sync\n                await _lib_supabaseMediaService__WEBPACK_IMPORTED_MODULE_1__.SupabaseMediaService.updateSyncStatus(publicId, 'pending');\n                results.deleted.push(publicId);\n                console.log(`[Cloudinary Media API] Marked for deletion: ${publicId}`);\n            } catch (error) {\n                const errorMsg = error instanceof Error ? error.message : 'Unknown error';\n                results.failed.push({\n                    public_id: publicId,\n                    error: errorMsg\n                });\n                console.error(`[Cloudinary Media API] Failed to delete ${publicId}:`, error);\n            }\n        }\n        // Trigger background sync to delete from Cloudinary\n        // Note: In production, this should be handled by a background job or webhook\n        try {\n            await _lib_bidirectionalSyncService__WEBPACK_IMPORTED_MODULE_2__.BidirectionalSyncService.performFullSync({\n                batch_size: 50\n            });\n        } catch (syncError) {\n            console.warn('[Cloudinary Media API] Background sync failed:', syncError);\n        // Don't fail the request if sync fails - it will be retried\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: results.failed.length === 0,\n            message: `Deleted ${results.deleted.length} items, ${results.failed.length} failed`,\n            deleted: results.deleted,\n            failed: results.failed,\n            timestamp: new Date().toISOString()\n        });\n    } catch (error) {\n        console.error('[Cloudinary Media API] Delete operation failed:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: error instanceof Error ? error.message : 'Unknown error',\n            message: 'Delete operation failed',\n            timestamp: new Date().toISOString()\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/cloudinary/media/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/bidirectionalSyncService.ts":
/*!*********************************************!*\
  !*** ./src/lib/bidirectionalSyncService.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BidirectionalSyncService: () => (/* binding */ BidirectionalSyncService)\n/* harmony export */ });\n/* harmony import */ var _supabaseMediaService__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabaseMediaService */ \"(rsc)/./src/lib/supabaseMediaService.ts\");\n/**\n * Bidirectional Sync Service\n * \n * Enterprise-grade bidirectional synchronization between Cloudinary and Supabase.\n * Ensures 100% persistent mirroring with conflict resolution and error recovery.\n * \n * Features:\n * - 🔄 Perfect bidirectional sync\n * - 🛡️ Conflict resolution\n * - 🔄 Automatic retry mechanisms\n * - 📊 Real-time sync status\n * - 🔍 Comprehensive audit trail\n * - ⚡ Batch processing for performance\n * - 🚨 Error recovery and notifications\n * - 📱 Webhook integration ready\n * \n * <AUTHOR> Project Team\n * @version 1.0.0\n */ \n// Server-side Cloudinary import\nlet cloudinary = null;\n// Initialize Cloudinary on server-side\nasync function initCloudinary() {\n    if ( true && !cloudinary) {\n        const cloudinaryModule = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/lodash\"), __webpack_require__.e(\"vendor-chunks/cloudinary\"), __webpack_require__.e(\"vendor-chunks/q\")]).then(__webpack_require__.t.bind(__webpack_require__, /*! cloudinary */ \"(rsc)/./node_modules/cloudinary/cloudinary.js\", 23));\n        cloudinary = cloudinaryModule.v2;\n        // Configure Cloudinary\n        cloudinary.config({\n            cloud_name: \"dvwaviwn0\",\n            api_key: process.env.CLOUDINARY_API_KEY,\n            api_secret: process.env.CLOUDINARY_API_SECRET,\n            secure: true\n        });\n    }\n    return cloudinary;\n}\n/**\n * Bidirectional Sync Service Class\n */ class BidirectionalSyncService {\n    static{\n        this.BATCH_SIZE = 100;\n    }\n    static{\n        this.MAX_RETRIES = 3;\n    }\n    static{\n        this.RETRY_DELAY = 1000 // 1 second\n        ;\n    }\n    /**\n   * Sync a single asset by public ID (for upload widget integration)\n   */ static async syncSingleAsset(publicId) {\n        const startTime = Date.now();\n        try {\n            console.log(`[BidirectionalSyncService] Syncing single asset: ${publicId}`);\n            const cloudinaryInstance = await initCloudinary();\n            if (!cloudinaryInstance) {\n                throw new Error('Cloudinary not available for sync operations');\n            }\n            // Fetch the specific resource from Cloudinary\n            const resource = await cloudinaryInstance.api.resource(publicId, {\n                resource_type: 'auto'\n            });\n            // Sync to database\n            const syncResult = await this.syncSingleResourceToDatabase(resource);\n            const duration_ms = Date.now() - startTime;\n            return {\n                success: true,\n                synced_items: syncResult.created ? 1 : 0,\n                updated_items: syncResult.updated ? 1 : 0,\n                deleted_items: 0,\n                errors: [],\n                duration_ms\n            };\n        } catch (error) {\n            const duration_ms = Date.now() - startTime;\n            const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n            console.error(`[BidirectionalSyncService] Single asset sync failed for ${publicId}:`, error);\n            return {\n                success: false,\n                synced_items: 0,\n                updated_items: 0,\n                deleted_items: 0,\n                errors: [\n                    errorMessage\n                ],\n                duration_ms\n            };\n        }\n    }\n    /**\n   * Perform complete bidirectional sync\n   */ static async performFullSync(options = {}) {\n        const startTime = Date.now();\n        try {\n            console.log('[BidirectionalSyncService] Starting full bidirectional sync...');\n            // Log sync start\n            await _supabaseMediaService__WEBPACK_IMPORTED_MODULE_0__.SupabaseMediaService.logSyncOperation({\n                operation: 'update',\n                status: 'pending',\n                cloudinary_public_id: 'FULL_SYNC',\n                source: 'admin',\n                operation_data: {\n                    sync_type: 'full',\n                    options\n                }\n            });\n            // Step 1: Sync from Cloudinary to Database\n            const cloudinaryToDbResult = await this.syncCloudinaryToDatabase(options);\n            // Step 2: Sync from Database to Cloudinary (handle pending operations)\n            const dbToCloudinaryResult = await this.syncDatabaseToCloudinary(options);\n            // Step 3: Clean up orphaned records\n            const cleanupResult = await this.cleanupOrphanedRecords();\n            const duration_ms = Date.now() - startTime;\n            const result = {\n                success: cloudinaryToDbResult.success && dbToCloudinaryResult.success,\n                synced_items: cloudinaryToDbResult.synced_items + dbToCloudinaryResult.synced_items,\n                updated_items: cloudinaryToDbResult.updated_items + dbToCloudinaryResult.updated_items,\n                deleted_items: cloudinaryToDbResult.deleted_items + dbToCloudinaryResult.deleted_items + cleanupResult.deleted_items,\n                errors: [\n                    ...cloudinaryToDbResult.errors,\n                    ...dbToCloudinaryResult.errors,\n                    ...cleanupResult.errors\n                ],\n                duration_ms,\n                last_cursor: cloudinaryToDbResult.last_cursor\n            };\n            // Log sync completion\n            await _supabaseMediaService__WEBPACK_IMPORTED_MODULE_0__.SupabaseMediaService.logSyncOperation({\n                operation: 'update',\n                status: result.success ? 'synced' : 'error',\n                cloudinary_public_id: 'FULL_SYNC',\n                source: 'admin',\n                processing_time_ms: duration_ms,\n                operation_data: result,\n                error_message: result.errors.length > 0 ? result.errors.join('; ') : undefined\n            });\n            console.log('[BidirectionalSyncService] Full sync completed:', result);\n            return result;\n        } catch (error) {\n            const duration_ms = Date.now() - startTime;\n            const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n            console.error('[BidirectionalSyncService] Full sync failed:', error);\n            // Log sync error\n            await _supabaseMediaService__WEBPACK_IMPORTED_MODULE_0__.SupabaseMediaService.logSyncOperation({\n                operation: 'update',\n                status: 'error',\n                cloudinary_public_id: 'FULL_SYNC',\n                source: 'admin',\n                processing_time_ms: duration_ms,\n                error_message: errorMessage\n            });\n            return {\n                success: false,\n                synced_items: 0,\n                updated_items: 0,\n                deleted_items: 0,\n                errors: [\n                    errorMessage\n                ],\n                duration_ms\n            };\n        }\n    }\n    /**\n   * Sync from Cloudinary to Database\n   */ static async syncCloudinaryToDatabase(options) {\n        const cloudinaryInstance = await initCloudinary();\n        if (!cloudinaryInstance) {\n            throw new Error('Cloudinary not available for sync operations');\n        }\n        const startTime = Date.now();\n        let synced_items = 0;\n        let updated_items = 0;\n        const deleted_items = 0;\n        const errors = [];\n        let next_cursor;\n        let last_cursor;\n        try {\n            console.log('[BidirectionalSyncService] Syncing Cloudinary → Database...');\n            const batchSize = options.batch_size || this.BATCH_SIZE;\n            // Build search expression\n            let expression = 'resource_type:image OR resource_type:video';\n            if (options.resource_type_filter) {\n                expression = `resource_type:${options.resource_type_filter}`;\n            }\n            if (options.folder_filter) {\n                expression += ` AND folder:${options.folder_filter}/*`;\n            }\n            // Fetch resources in batches\n            do {\n                try {\n                    const result = await cloudinaryInstance.search.expression(expression).sort_by('created_at', 'desc').max_results(batchSize).next_cursor(next_cursor).execute();\n                    // Process each resource\n                    for (const resource of result.resources){\n                        try {\n                            const syncResult = await this.syncSingleResourceToDatabase(resource);\n                            if (syncResult.created) {\n                                synced_items++;\n                            } else if (syncResult.updated) {\n                                updated_items++;\n                            }\n                        } catch (error) {\n                            const errorMsg = `Failed to sync ${resource.public_id}: ${error}`;\n                            errors.push(errorMsg);\n                            console.error('[BidirectionalSyncService]', errorMsg);\n                        }\n                    }\n                    next_cursor = result.next_cursor;\n                    last_cursor = next_cursor;\n                    console.log(`[BidirectionalSyncService] Processed batch: ${result.resources.length} items`);\n                } catch (batchError) {\n                    const errorMsg = `Batch sync failed: ${batchError}`;\n                    errors.push(errorMsg);\n                    console.error('[BidirectionalSyncService]', errorMsg);\n                    break;\n                }\n            }while (next_cursor);\n            const duration_ms = Date.now() - startTime;\n            return {\n                success: errors.length === 0,\n                synced_items,\n                updated_items,\n                deleted_items,\n                errors,\n                duration_ms,\n                last_cursor\n            };\n        } catch (error) {\n            const duration_ms = Date.now() - startTime;\n            const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n            return {\n                success: false,\n                synced_items,\n                updated_items,\n                deleted_items,\n                errors: [\n                    ...errors,\n                    errorMessage\n                ],\n                duration_ms\n            };\n        }\n    }\n    /**\n   * Sync single Cloudinary resource to database\n   */ static async syncSingleResourceToDatabase(resource) {\n        try {\n            // Check if asset exists in database\n            const existingAsset = await _supabaseMediaService__WEBPACK_IMPORTED_MODULE_0__.SupabaseMediaService.getMediaAssetByPublicId(resource.public_id);\n            // Convert Cloudinary resource to MediaAsset format\n            const mediaAsset = {\n                cloudinary_public_id: resource.public_id,\n                cloudinary_version: resource.version,\n                cloudinary_signature: resource.signature,\n                cloudinary_etag: resource.etag,\n                original_filename: resource.original_filename,\n                file_size: resource.bytes,\n                mime_type: this.getMimeTypeFromFormat(resource.format, resource.resource_type),\n                format: resource.format,\n                width: resource.width,\n                height: resource.height,\n                folder: resource.folder,\n                tags: resource.tags || [],\n                secure_url: resource.secure_url,\n                url: resource.url,\n                resource_type: resource.resource_type,\n                cloudinary_created_at: resource.created_at,\n                sync_status: 'synced'\n            };\n            // Determine if this is a create or update\n            const isUpdate = !!existingAsset;\n            const hasChanges = isUpdate ? this.hasResourceChanged(existingAsset, resource) : true;\n            if (hasChanges) {\n                await _supabaseMediaService__WEBPACK_IMPORTED_MODULE_0__.SupabaseMediaService.upsertMediaAsset(mediaAsset);\n                // Log the operation\n                await _supabaseMediaService__WEBPACK_IMPORTED_MODULE_0__.SupabaseMediaService.logSyncOperation({\n                    operation: isUpdate ? 'update' : 'upload',\n                    status: 'synced',\n                    media_asset_id: existingAsset?.id,\n                    cloudinary_public_id: resource.public_id,\n                    source: 'cloudinary',\n                    file_size: resource.bytes,\n                    operation_data: {\n                        cloudinary_resource: resource,\n                        changes_detected: hasChanges\n                    }\n                });\n            }\n            return {\n                created: !isUpdate && hasChanges,\n                updated: isUpdate && hasChanges\n            };\n        } catch (error) {\n            console.error('[BidirectionalSyncService] Failed to sync resource to database:', error);\n            throw error;\n        }\n    }\n    /**\n   * Check if Cloudinary resource has changed compared to database record\n   */ static hasResourceChanged(dbAsset, cloudinaryResource) {\n        return dbAsset.cloudinary_version !== cloudinaryResource.version || dbAsset.cloudinary_signature !== cloudinaryResource.signature || dbAsset.file_size !== cloudinaryResource.bytes || JSON.stringify(dbAsset.tags.sort()) !== JSON.stringify((cloudinaryResource.tags || []).sort());\n    }\n    /**\n   * Get MIME type from Cloudinary format and resource type\n   */ static getMimeTypeFromFormat(format, resourceType) {\n        if (resourceType === 'image') {\n            const imageFormats = {\n                'jpg': 'image/jpeg',\n                'jpeg': 'image/jpeg',\n                'png': 'image/png',\n                'gif': 'image/gif',\n                'webp': 'image/webp',\n                'svg': 'image/svg+xml',\n                'bmp': 'image/bmp',\n                'tiff': 'image/tiff'\n            };\n            return imageFormats[format.toLowerCase()] || 'image/jpeg';\n        }\n        if (resourceType === 'video') {\n            const videoFormats = {\n                'mp4': 'video/mp4',\n                'webm': 'video/webm',\n                'mov': 'video/quicktime',\n                'avi': 'video/x-msvideo',\n                'mkv': 'video/x-matroska'\n            };\n            return videoFormats[format.toLowerCase()] || 'video/mp4';\n        }\n        return 'application/octet-stream';\n    }\n    /**\n   * Sync from Database to Cloudinary (handle pending operations)\n   */ static async syncDatabaseToCloudinary(options) {\n        const startTime = Date.now();\n        const synced_items = 0;\n        let updated_items = 0;\n        let deleted_items = 0;\n        const errors = [];\n        try {\n            console.log('[BidirectionalSyncService] Syncing Database → Cloudinary...');\n            // Get pending operations from database\n            const pendingAssets = await _supabaseMediaService__WEBPACK_IMPORTED_MODULE_0__.SupabaseMediaService.searchMediaAssets({\n                sync_status: 'pending',\n                limit: options.batch_size || this.BATCH_SIZE\n            });\n            for (const asset of pendingAssets.assets){\n                try {\n                    if (asset.deleted_at) {\n                        // Handle pending deletes\n                        const deleteResult = await this.deleteFromCloudinary(asset.cloudinary_public_id, asset.resource_type);\n                        if (deleteResult.success) {\n                            deleted_items++;\n                            await _supabaseMediaService__WEBPACK_IMPORTED_MODULE_0__.SupabaseMediaService.updateSyncStatus(asset.cloudinary_public_id, 'synced');\n                        } else {\n                            errors.push(`Failed to delete ${asset.cloudinary_public_id} from Cloudinary`);\n                            await _supabaseMediaService__WEBPACK_IMPORTED_MODULE_0__.SupabaseMediaService.updateSyncStatus(asset.cloudinary_public_id, 'error', deleteResult.error);\n                        }\n                    } else {\n                        // Handle pending updates (tags, metadata changes)\n                        const updateResult = await this.updateInCloudinary(asset);\n                        if (updateResult.success) {\n                            updated_items++;\n                            await _supabaseMediaService__WEBPACK_IMPORTED_MODULE_0__.SupabaseMediaService.updateSyncStatus(asset.cloudinary_public_id, 'synced');\n                        } else {\n                            errors.push(`Failed to update ${asset.cloudinary_public_id} in Cloudinary`);\n                            await _supabaseMediaService__WEBPACK_IMPORTED_MODULE_0__.SupabaseMediaService.updateSyncStatus(asset.cloudinary_public_id, 'error', updateResult.error);\n                        }\n                    }\n                } catch (error) {\n                    const errorMsg = `Failed to sync ${asset.cloudinary_public_id} to Cloudinary: ${error}`;\n                    errors.push(errorMsg);\n                    console.error('[BidirectionalSyncService]', errorMsg);\n                }\n            }\n            const duration_ms = Date.now() - startTime;\n            return {\n                success: errors.length === 0,\n                synced_items,\n                updated_items,\n                deleted_items,\n                errors,\n                duration_ms\n            };\n        } catch (error) {\n            const duration_ms = Date.now() - startTime;\n            const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n            return {\n                success: false,\n                synced_items,\n                updated_items,\n                deleted_items,\n                errors: [\n                    ...errors,\n                    errorMessage\n                ],\n                duration_ms\n            };\n        }\n    }\n    /**\n   * Delete asset from Cloudinary\n   */ static async deleteFromCloudinary(publicId, resourceType) {\n        const cloudinaryInstance = await initCloudinary();\n        if (!cloudinaryInstance) {\n            return {\n                success: false,\n                error: 'Cloudinary not available'\n            };\n        }\n        try {\n            await cloudinaryInstance.uploader.destroy(publicId, {\n                resource_type: resourceType\n            });\n            return {\n                success: true\n            };\n        } catch (error) {\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : 'Unknown error'\n            };\n        }\n    }\n    /**\n   * Update asset in Cloudinary\n   */ static async updateInCloudinary(asset) {\n        const cloudinaryInstance = await initCloudinary();\n        if (!cloudinaryInstance) {\n            return {\n                success: false,\n                error: 'Cloudinary not available'\n            };\n        }\n        try {\n            // Update tags and context in Cloudinary\n            await cloudinaryInstance.uploader.add_tag(asset.tags.join(','), [\n                asset.cloudinary_public_id\n            ]);\n            if (asset.description || asset.alt_text) {\n                await cloudinaryInstance.uploader.update_metadata({\n                    description: asset.description,\n                    alt_text: asset.alt_text\n                }, [\n                    asset.cloudinary_public_id\n                ]);\n            }\n            return {\n                success: true\n            };\n        } catch (error) {\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : 'Unknown error'\n            };\n        }\n    }\n    /**\n   * Clean up orphaned records\n   */ static async cleanupOrphanedRecords() {\n        const startTime = Date.now();\n        let deleted_items = 0;\n        const errors = [];\n        try {\n            console.log('[BidirectionalSyncService] Cleaning up orphaned records...');\n            // Get all assets from database\n            const allAssets = await _supabaseMediaService__WEBPACK_IMPORTED_MODULE_0__.SupabaseMediaService.searchMediaAssets({\n                limit: 1000 // Process in chunks for large datasets\n            });\n            for (const asset of allAssets.assets){\n                try {\n                    // Check if asset still exists in Cloudinary\n                    const exists = await this.checkAssetExistsInCloudinary(asset.cloudinary_public_id, asset.resource_type);\n                    if (!exists) {\n                        // Asset doesn't exist in Cloudinary, soft delete from database\n                        await _supabaseMediaService__WEBPACK_IMPORTED_MODULE_0__.SupabaseMediaService.softDeleteMediaAsset(asset.cloudinary_public_id, 'system');\n                        deleted_items++;\n                        console.log(`[BidirectionalSyncService] Cleaned up orphaned record: ${asset.cloudinary_public_id}`);\n                    }\n                } catch (error) {\n                    const errorMsg = `Failed to check/cleanup ${asset.cloudinary_public_id}: ${error}`;\n                    errors.push(errorMsg);\n                    console.error('[BidirectionalSyncService]', errorMsg);\n                }\n            }\n            const duration_ms = Date.now() - startTime;\n            return {\n                success: errors.length === 0,\n                synced_items: 0,\n                updated_items: 0,\n                deleted_items,\n                errors,\n                duration_ms\n            };\n        } catch (error) {\n            const duration_ms = Date.now() - startTime;\n            const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n            return {\n                success: false,\n                synced_items: 0,\n                updated_items: 0,\n                deleted_items,\n                errors: [\n                    ...errors,\n                    errorMessage\n                ],\n                duration_ms\n            };\n        }\n    }\n    /**\n   * Check if asset exists in Cloudinary\n   */ static async checkAssetExistsInCloudinary(publicId, resourceType) {\n        const cloudinaryInstance = await initCloudinary();\n        if (!cloudinaryInstance) {\n            return false;\n        }\n        try {\n            await cloudinaryInstance.api.resource(publicId, {\n                resource_type: resourceType\n            });\n            return true;\n        } catch (error) {\n            // If error code is 404, asset doesn't exist\n            if (error && typeof error === 'object' && 'http_code' in error && error.http_code === 404) {\n                return false;\n            }\n            // For other errors, assume it exists to be safe\n            const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n            console.warn(`[BidirectionalSyncService] Could not verify asset existence: ${errorMessage}`);\n            return true;\n        }\n    }\n    /**\n   * Handle webhook from Cloudinary\n   */ static async handleCloudinaryWebhook(webhookData) {\n        try {\n            console.log('[BidirectionalSyncService] Processing Cloudinary webhook:', webhookData);\n            const notification_type = webhookData.notification_type;\n            const public_id = webhookData.public_id;\n            // Log webhook received\n            await _supabaseMediaService__WEBPACK_IMPORTED_MODULE_0__.SupabaseMediaService.logSyncOperation({\n                operation: this.mapWebhookToOperation(notification_type),\n                status: 'pending',\n                cloudinary_public_id: public_id,\n                source: 'webhook',\n                webhook_data: webhookData\n            });\n            switch(notification_type){\n                case 'upload':\n                case 'update':\n                    await this.handleWebhookUploadUpdate(webhookData);\n                    break;\n                case 'delete':\n                    await this.handleWebhookDelete(public_id);\n                    break;\n                default:\n                    console.log(`[BidirectionalSyncService] Unhandled webhook type: ${notification_type}`);\n            }\n        } catch (error) {\n            console.error('[BidirectionalSyncService] Webhook processing failed:', error);\n            throw error;\n        }\n    }\n    /**\n   * Map webhook notification type to operation\n   */ static mapWebhookToOperation(notificationType) {\n        switch(notificationType){\n            case 'upload':\n                return 'upload';\n            case 'delete':\n                return 'delete';\n            case 'update':\n                return 'update';\n            default:\n                return 'update';\n        }\n    }\n    /**\n   * Handle webhook upload/update\n   */ static async handleWebhookUploadUpdate(webhookData) {\n        // Fetch the resource details from Cloudinary\n        const cloudinaryInstance = await initCloudinary();\n        if (!cloudinaryInstance) {\n            throw new Error('Cloudinary not available');\n        }\n        try {\n            const resource = await cloudinaryInstance.api.resource(webhookData.public_id, {\n                resource_type: webhookData.resource_type\n            });\n            // Sync to database\n            await this.syncSingleResourceToDatabase(resource);\n            console.log(`[BidirectionalSyncService] Webhook sync completed for: ${webhookData.public_id}`);\n        } catch (error) {\n            console.error('[BidirectionalSyncService] Webhook upload/update failed:', error);\n            throw error;\n        }\n    }\n    /**\n   * Handle webhook delete\n   */ static async handleWebhookDelete(publicId) {\n        try {\n            // Soft delete from database\n            await _supabaseMediaService__WEBPACK_IMPORTED_MODULE_0__.SupabaseMediaService.softDeleteMediaAsset(publicId, 'webhook');\n            console.log(`[BidirectionalSyncService] Webhook delete completed for: ${publicId}`);\n        } catch (error) {\n            console.error('[BidirectionalSyncService] Webhook delete failed:', error);\n            throw error;\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/bidirectionalSyncService.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabaseMediaService.ts":
/*!*****************************************!*\
  !*** ./src/lib/supabaseMediaService.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SupabaseMediaService: () => (/* binding */ SupabaseMediaService)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/**\n * Supabase Media Service\n * \n * Enterprise-grade database service for media library with perfect bidirectional sync.\n * Handles all database operations for media assets with Supabase integration.\n * \n * Features:\n * - 🗄️ Complete CRUD operations for media assets\n * - 🔄 Sync status management\n * - 📊 Real-time statistics\n * - 🔍 Advanced search and filtering\n * - 📝 Audit trail logging\n * - 🔒 Enterprise-grade error handling\n * - ⚡ Optimized queries with indexes\n * \n * <AUTHOR> Project Team\n * @version 1.0.0\n */ \n/**\n * Supabase Media Service Class\n */ class SupabaseMediaService {\n    static{\n        this.supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(\"https://lkolpgpmdculqqfqyzaf.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY);\n    }\n    /**\n   * Create or update media asset in database\n   */ static async upsertMediaAsset(asset) {\n        try {\n            console.log('[SupabaseMediaService] Upserting media asset:', asset.cloudinary_public_id);\n            const { data, error } = await this.supabase.from('media_assets').upsert({\n                ...asset,\n                updated_at: new Date().toISOString(),\n                last_synced_at: new Date().toISOString()\n            }, {\n                onConflict: 'cloudinary_public_id'\n            }).select().single();\n            if (error) {\n                throw new Error(`Failed to upsert media asset: ${error.message}`);\n            }\n            return data;\n        } catch (error) {\n            console.error('[SupabaseMediaService] Upsert failed:', error);\n            throw error;\n        }\n    }\n    /**\n   * Get media asset by Cloudinary public ID\n   */ static async getMediaAssetByPublicId(publicId) {\n        try {\n            const { data, error } = await this.supabase.from('media_assets').select('*').eq('cloudinary_public_id', publicId).eq('deleted_at', null).single();\n            if (error && error.code !== 'PGRST116') {\n                throw new Error(`Failed to get media asset: ${error.message}`);\n            }\n            return data;\n        } catch (error) {\n            console.error('[SupabaseMediaService] Get asset failed:', error);\n            return null;\n        }\n    }\n    /**\n   * Search media assets with advanced filtering\n   */ static async searchMediaAssets(options = {}) {\n        try {\n            const { search, folder, resource_type, tags, sync_status, uploaded_by, date_from, date_to, min_size, max_size, page = 1, limit = 50, sort_by = 'created_at', sort_order = 'desc' } = options;\n            let query = this.supabase.from('media_assets').select('*', {\n                count: 'exact'\n            }).is('deleted_at', null);\n            // Apply filters\n            if (search) {\n                query = query.or(`original_filename.ilike.%${search}%,display_name.ilike.%${search}%,description.ilike.%${search}%`);\n            }\n            if (folder) {\n                query = query.eq('folder', folder);\n            }\n            if (resource_type) {\n                query = query.eq('resource_type', resource_type);\n            }\n            if (tags && tags.length > 0) {\n                query = query.overlaps('tags', tags);\n            }\n            if (sync_status) {\n                query = query.eq('sync_status', sync_status);\n            }\n            if (uploaded_by) {\n                query = query.eq('uploaded_by', uploaded_by);\n            }\n            if (date_from) {\n                query = query.gte('created_at', date_from);\n            }\n            if (date_to) {\n                query = query.lte('created_at', date_to);\n            }\n            if (min_size) {\n                query = query.gte('file_size', min_size);\n            }\n            if (max_size) {\n                query = query.lte('file_size', max_size);\n            }\n            // Apply sorting and pagination\n            const offset = (page - 1) * limit;\n            query = query.order(sort_by, {\n                ascending: sort_order === 'asc'\n            }).range(offset, offset + limit - 1);\n            const { data, error, count } = await query;\n            if (error) {\n                throw new Error(`Failed to search media assets: ${error.message}`);\n            }\n            const total = count || 0;\n            const totalPages = Math.ceil(total / limit);\n            return {\n                assets: data,\n                total,\n                page,\n                limit,\n                has_next: page < totalPages,\n                has_prev: page > 1\n            };\n        } catch (error) {\n            console.error('[SupabaseMediaService] Search failed:', error);\n            throw error;\n        }\n    }\n    /**\n   * Get media statistics\n   */ static async getMediaStats() {\n        try {\n            // Try the function first\n            const { data: functionData, error: functionError } = await this.supabase.rpc('get_media_statistics');\n            if (!functionError && functionData) {\n                return functionData[0];\n            }\n            // Fallback: Calculate stats manually\n            console.log('[SupabaseMediaService] Function not available, calculating stats manually...');\n            const { data: assets, error } = await this.supabase.from('media_assets').select('resource_type, file_size, sync_status').is('deleted_at', null);\n            if (error) {\n                console.error('[SupabaseMediaService] Failed to get media assets for stats:', error);\n                throw error;\n            }\n            const stats = {\n                total_assets: assets?.length || 0,\n                total_images: assets?.filter((a)=>a.resource_type === 'image').length || 0,\n                total_videos: assets?.filter((a)=>a.resource_type === 'video').length || 0,\n                total_raw: assets?.filter((a)=>a.resource_type === 'raw').length || 0,\n                total_size: assets?.reduce((sum, a)=>sum + (a.file_size || 0), 0) || 0,\n                synced_assets: assets?.filter((a)=>a.sync_status === 'synced').length || 0,\n                pending_assets: assets?.filter((a)=>a.sync_status === 'pending').length || 0,\n                error_assets: assets?.filter((a)=>a.sync_status === 'error').length || 0\n            };\n            return stats;\n        } catch (error) {\n            console.error('[SupabaseMediaService] Get stats failed:', error);\n            // Return default stats on error\n            return {\n                total_assets: 0,\n                total_images: 0,\n                total_videos: 0,\n                total_raw: 0,\n                total_size: 0,\n                synced_assets: 0,\n                pending_assets: 0,\n                error_assets: 0\n            };\n        }\n    }\n    /**\n   * Soft delete media asset\n   */ static async softDeleteMediaAsset(publicId, deletedBy) {\n        try {\n            console.log('[SupabaseMediaService] Soft deleting media asset:', publicId);\n            const { data, error } = await this.supabase.rpc('soft_delete_media_asset', {\n                asset_id: publicId,\n                deleted_by_user: deletedBy\n            });\n            if (error) {\n                throw new Error(`Failed to soft delete media asset: ${error.message}`);\n            }\n            return data;\n        } catch (error) {\n            console.error('[SupabaseMediaService] Soft delete failed:', error);\n            throw error;\n        }\n    }\n    /**\n   * Update sync status\n   */ static async updateSyncStatus(publicId, status, errorMessage) {\n        try {\n            const { data, error } = await this.supabase.rpc('update_media_sync_status', {\n                asset_id: publicId,\n                new_status: status,\n                error_msg: errorMessage\n            });\n            if (error) {\n                throw new Error(`Failed to update sync status: ${error.message}`);\n            }\n            return data;\n        } catch (error) {\n            console.error('[SupabaseMediaService] Update sync status failed:', error);\n            throw error;\n        }\n    }\n    /**\n   * Log sync operation\n   */ static async logSyncOperation(log) {\n        try {\n            const { data, error } = await this.supabase.from('media_sync_log').insert({\n                ...log,\n                created_at: new Date().toISOString()\n            }).select().single();\n            if (error) {\n                throw new Error(`Failed to log sync operation: ${error.message}`);\n            }\n            return data;\n        } catch (error) {\n            console.error('[SupabaseMediaService] Log sync operation failed:', error);\n            throw error;\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabaseMediaService.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcloudinary%2Fmedia%2Froute&page=%2Fapi%2Fcloudinary%2Fmedia%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcloudinary%2Fmedia%2Froute.ts&appDir=C%3A%5CUsers%5CDABBIE%5COneDrive%5CDesktop%5Clgu-project-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDABBIE%5COneDrive%5CDesktop%5Clgu-project-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();