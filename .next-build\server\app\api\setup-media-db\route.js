/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/setup-media-db/route";
exports.ids = ["app/api/setup-media-db/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fsetup-media-db%2Froute&page=%2Fapi%2Fsetup-media-db%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsetup-media-db%2Froute.ts&appDir=C%3A%5CUsers%5CDABBIE%5COneDrive%5CDesktop%5Clgu-project-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDABBIE%5COneDrive%5CDesktop%5Clgu-project-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fsetup-media-db%2Froute&page=%2Fapi%2Fsetup-media-db%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsetup-media-db%2Froute.ts&appDir=C%3A%5CUsers%5CDABBIE%5COneDrive%5CDesktop%5Clgu-project-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDABBIE%5COneDrive%5CDesktop%5Clgu-project-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_DABBIE_OneDrive_Desktop_lgu_project_app_src_app_api_setup_media_db_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/setup-media-db/route.ts */ \"(rsc)/./src/app/api/setup-media-db/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/setup-media-db/route\",\n        pathname: \"/api/setup-media-db\",\n        filename: \"route\",\n        bundlePath: \"app/api/setup-media-db/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\app\\\\api\\\\setup-media-db\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_DABBIE_OneDrive_Desktop_lgu_project_app_src_app_api_setup_media_db_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fsetup-media-db%2Froute&page=%2Fapi%2Fsetup-media-db%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsetup-media-db%2Froute.ts&appDir=C%3A%5CUsers%5CDABBIE%5COneDrive%5CDesktop%5Clgu-project-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDABBIE%5COneDrive%5CDesktop%5Clgu-project-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/setup-media-db/route.ts":
/*!*********************************************!*\
  !*** ./src/app/api/setup-media-db/route.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabaseMediaService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabaseMediaService */ \"(rsc)/./src/lib/supabaseMediaService.ts\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/**\n * Setup Media Database API Route\n * \n * Verifies and initializes the media library database setup.\n * Ensures all tables, functions, and configurations are properly set up\n * for bidirectional sync with Cloudinary.\n * \n * Features:\n * - 🔍 Database schema verification\n * - 🛠️ Function availability checks\n * - 📊 Initial statistics gathering\n * - 🔄 Sync status validation\n * - 🚀 Ready-to-use confirmation\n * \n * <AUTHOR> Project Team\n * @version 1.0.0\n */ \n\n\n/**\n * GET /api/setup-media-db\n * Verify media database setup and configuration\n */ async function GET() {\n    try {\n        console.log('[Setup Media DB] Verifying database setup...');\n        const supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__.createClient)(\"https://lkolpgpmdculqqfqyzaf.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY);\n        const setupStatus = {\n            database_connected: false,\n            tables_exist: false,\n            functions_exist: false,\n            indexes_exist: false,\n            rls_enabled: false,\n            initial_stats: null,\n            errors: [],\n            warnings: []\n        };\n        // 1. Test database connection\n        try {\n            const { error } = await supabase.from('media_assets').select('count').limit(1);\n            if (error) throw error;\n            setupStatus.database_connected = true;\n            console.log('[Setup Media DB] ✅ Database connection successful');\n        } catch (error) {\n            setupStatus.errors.push(`Database connection failed: ${error}`);\n            console.error('[Setup Media DB] ❌ Database connection failed:', error);\n        }\n        // 2. Check if required tables exist by testing direct access\n        try {\n            const requiredTables = [\n                'media_assets',\n                'media_sync_log',\n                'media_usage',\n                'media_collections',\n                'media_collection_items'\n            ];\n            const existingTables = [];\n            for (const tableName of requiredTables){\n                try {\n                    // Test direct access to each table\n                    const { error } = await supabase.from(tableName).select('count').limit(1);\n                    if (!error) {\n                        existingTables.push(tableName);\n                    }\n                } catch (tableError) {\n                    console.warn(`[Setup Media DB] Table ${tableName} access failed:`, tableError);\n                }\n            }\n            const missingTables = requiredTables.filter((table)=>!existingTables.includes(table));\n            if (missingTables.length === 0) {\n                setupStatus.tables_exist = true;\n                console.log('[Setup Media DB] ✅ All required tables exist and are accessible');\n            } else {\n                setupStatus.errors.push(`Missing or inaccessible tables: ${missingTables.join(', ')}`);\n                console.error('[Setup Media DB] ❌ Missing tables:', missingTables);\n            }\n        } catch (error) {\n            setupStatus.errors.push(`Table verification failed: ${error}`);\n            console.error('[Setup Media DB] ❌ Table verification failed:', error);\n        }\n        // 3. Check if required functions exist\n        try {\n            const { data: functions, error } = await supabase.from('information_schema.routines').select('routine_name').eq('routine_schema', 'public').in('routine_name', [\n                'soft_delete_media_asset',\n                'restore_media_asset',\n                'update_media_sync_status',\n                'get_media_statistics',\n                'cleanup_old_sync_logs'\n            ]);\n            if (error) throw error;\n            const requiredFunctions = [\n                'soft_delete_media_asset',\n                'restore_media_asset',\n                'update_media_sync_status',\n                'get_media_statistics',\n                'cleanup_old_sync_logs'\n            ];\n            const existingFunctions = functions?.map((f)=>f.routine_name) || [];\n            const missingFunctions = requiredFunctions.filter((func)=>!existingFunctions.includes(func));\n            if (missingFunctions.length === 0) {\n                setupStatus.functions_exist = true;\n                console.log('[Setup Media DB] ✅ All required functions exist');\n            } else {\n                setupStatus.errors.push(`Missing functions: ${missingFunctions.join(', ')}`);\n                console.error('[Setup Media DB] ❌ Missing functions:', missingFunctions);\n            }\n        } catch (error) {\n            setupStatus.errors.push(`Function verification failed: ${error}`);\n            console.error('[Setup Media DB] ❌ Function verification failed:', error);\n        }\n        // 4. Check indexes\n        try {\n            const { data: indexes, error } = await supabase.from('pg_indexes').select('indexname').eq('schemaname', 'public').like('indexname', 'idx_media_%');\n            if (error) throw error;\n            const mediaIndexes = indexes?.length || 0;\n            if (mediaIndexes >= 10) {\n                setupStatus.indexes_exist = true;\n                console.log(`[Setup Media DB] ✅ Found ${mediaIndexes} media indexes`);\n            } else {\n                setupStatus.warnings.push(`Only ${mediaIndexes} media indexes found, expected at least 10`);\n                console.warn(`[Setup Media DB] ⚠️ Only ${mediaIndexes} media indexes found`);\n            }\n        } catch (error) {\n            setupStatus.warnings.push(`Index verification failed: ${error}`);\n            console.warn('[Setup Media DB] ⚠️ Index verification failed:', error);\n        }\n        // 5. Test RLS policies\n        try {\n            // Try to access media_assets with authenticated role simulation\n            const { error } = await supabase.from('media_assets').select('count').limit(1);\n            if (!error) {\n                setupStatus.rls_enabled = true;\n                console.log('[Setup Media DB] ✅ RLS policies working');\n            }\n        } catch (error) {\n            setupStatus.warnings.push(`RLS verification failed: ${error}`);\n            console.warn('[Setup Media DB] ⚠️ RLS verification failed:', error);\n        }\n        // 6. Get initial statistics\n        try {\n            const stats = await _lib_supabaseMediaService__WEBPACK_IMPORTED_MODULE_1__.SupabaseMediaService.getMediaStats();\n            setupStatus.initial_stats = stats;\n            console.log('[Setup Media DB] ✅ Statistics retrieved:', stats);\n        } catch (error) {\n            setupStatus.warnings.push(`Statistics retrieval failed: ${error}`);\n            console.warn('[Setup Media DB] ⚠️ Statistics retrieval failed:', error);\n        }\n        // Determine overall status\n        const isFullySetup = setupStatus.database_connected && setupStatus.tables_exist && setupStatus.functions_exist && setupStatus.errors.length === 0;\n        const response = {\n            success: isFullySetup,\n            message: isFullySetup ? '🎉 Media library database is fully configured and ready for bidirectional sync!' : '⚠️ Media library database setup has issues that need attention',\n            setup_status: setupStatus,\n            next_steps: isFullySetup ? [\n                '✅ Database is ready for production use',\n                '🔄 Test the sync functionality in admin panel',\n                '📱 Configure Cloudinary webhooks for real-time sync',\n                '🚀 Start uploading and managing media files'\n            ] : [\n                '📋 Run the complete Supabase script from docs/full-complete-supabase-script.md',\n                '🔍 Check database connection and permissions',\n                '🛠️ Verify all environment variables are set correctly',\n                '🔄 Re-run this setup verification'\n            ],\n            environment_check: {\n                supabase_url: !!\"https://lkolpgpmdculqqfqyzaf.supabase.co\",\n                supabase_service_key: !!process.env.SUPABASE_SERVICE_ROLE_KEY,\n                cloudinary_cloud_name: !!\"dvwaviwn0\",\n                cloudinary_api_key: !!process.env.CLOUDINARY_API_KEY,\n                cloudinary_api_secret: !!process.env.CLOUDINARY_API_SECRET\n            },\n            timestamp: new Date().toISOString()\n        };\n        console.log(`[Setup Media DB] Setup verification completed. Success: ${isFullySetup}`);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(response);\n    } catch (error) {\n        console.error('[Setup Media DB] Setup verification failed:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Setup verification failed',\n            message: error instanceof Error ? error.message : 'Unknown error',\n            timestamp: new Date().toISOString()\n        }, {\n            status: 500\n        });\n    }\n}\n/**\n * POST /api/setup-media-db\n * Initialize or repair media database setup using Supabase RPC\n */ async function POST() {\n    try {\n        console.log('[Setup Media DB] Starting automated database setup...');\n        const supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__.createClient)(\"https://lkolpgpmdculqqfqyzaf.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY);\n        const setupResults = {\n            steps_completed: [],\n            errors: [],\n            warnings: []\n        };\n        // Step 1: Create the setup function if it doesn't exist\n        try {\n            console.log('[Setup Media DB] Creating setup function...');\n            const setupFunctionSQL = `\n        CREATE OR REPLACE FUNCTION execute_setup_sql(sql_statements TEXT[])\n        RETURNS TABLE(step_number INTEGER, status TEXT, message TEXT) AS $$\n        DECLARE\n            stmt TEXT;\n            step_num INTEGER := 1;\n        BEGIN\n            FOREACH stmt IN ARRAY sql_statements\n            LOOP\n                BEGIN\n                    EXECUTE stmt;\n                    RETURN QUERY SELECT step_num, 'success'::TEXT, ('Executed: ' || LEFT(stmt, 50) || '...')::TEXT;\n                EXCEPTION WHEN OTHERS THEN\n                    RETURN QUERY SELECT step_num, 'error'::TEXT, (SQLERRM || ' - SQL: ' || LEFT(stmt, 50) || '...')::TEXT;\n                END;\n                step_num := step_num + 1;\n            END LOOP;\n        END;\n        $$ LANGUAGE plpgsql SECURITY DEFINER;\n      `;\n            const { error: functionError } = await supabase.rpc('exec', {\n                sql: setupFunctionSQL\n            });\n            if (functionError) {\n                // Try alternative approach - create function directly\n                const { error: directError } = await supabase.from('pg_proc').select('proname').eq('proname', 'execute_setup_sql').single();\n                if (directError) {\n                    setupResults.warnings.push('Could not verify setup function creation');\n                }\n            }\n            setupResults.steps_completed.push('Setup function created');\n        } catch (error) {\n            setupResults.errors.push(`Setup function creation failed: ${error}`);\n        }\n        // Step 2: Execute database setup in chunks\n        const sqlChunks = [\n            // Chunk 1: Types and basic tables\n            [\n                `DO $$ BEGIN CREATE TYPE user_status AS ENUM ('ACTIVE', 'INACTIVE', 'SUSPENDED'); EXCEPTION WHEN duplicate_object THEN null; END $$;`,\n                `DO $$ BEGIN CREATE TYPE personnel_status AS ENUM ('Active', 'Inactive', 'On Leave', 'Suspended'); EXCEPTION WHEN duplicate_object THEN null; END $$;`,\n                `DO $$ BEGIN CREATE TYPE media_sync_status AS ENUM ('synced', 'pending', 'error'); EXCEPTION WHEN duplicate_object THEN null; END $$;`,\n                `DO $$ BEGIN CREATE TYPE media_sync_operation AS ENUM ('upload', 'delete', 'update', 'restore'); EXCEPTION WHEN duplicate_object THEN null; END $$;`,\n                `CREATE TABLE IF NOT EXISTS users (\n          id BIGSERIAL PRIMARY KEY,\n          email VARCHAR(255) UNIQUE NOT NULL,\n          name VARCHAR(255) NOT NULL,\n          password VARCHAR(255) NOT NULL,\n          phone VARCHAR(50),\n          address TEXT,\n          role VARCHAR(50) DEFAULT 'user',\n          status user_status DEFAULT 'ACTIVE',\n          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\n          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()\n        );`\n            ],\n            // Chunk 2: Personnel tables\n            [\n                `CREATE TABLE IF NOT EXISTS personnel (\n          id BIGSERIAL PRIMARY KEY,\n          name VARCHAR(255) NOT NULL,\n          email VARCHAR(255) UNIQUE NOT NULL,\n          phone VARCHAR(50),\n          address TEXT,\n          profile_photo VARCHAR(500),\n          department VARCHAR(255) NOT NULL,\n          position VARCHAR(255),\n          hire_date DATE,\n          status personnel_status DEFAULT 'Active',\n          biography TEXT,\n          spouse_name VARCHAR(255),\n          spouse_occupation VARCHAR(255),\n          children_count VARCHAR(10),\n          emergency_contact VARCHAR(50),\n          children_names TEXT,\n          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\n          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()\n        );`,\n                `CREATE TABLE IF NOT EXISTS personnel_documents (\n          id BIGSERIAL PRIMARY KEY,\n          filename VARCHAR(255) NOT NULL,\n          original_name VARCHAR(255) NOT NULL,\n          mime_type VARCHAR(100) NOT NULL,\n          size INTEGER NOT NULL,\n          path VARCHAR(500) NOT NULL,\n          personnel_id BIGINT NOT NULL REFERENCES personnel(id) ON DELETE CASCADE,\n          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\n          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()\n        );`\n            ]\n        ];\n        // Execute each chunk\n        for(let i = 0; i < sqlChunks.length; i++){\n            try {\n                console.log(`[Setup Media DB] Executing chunk ${i + 1}...`);\n                const { data, error } = await supabase.rpc('execute_setup_sql', {\n                    sql_statements: sqlChunks[i]\n                });\n                if (error) {\n                    setupResults.errors.push(`Chunk ${i + 1} failed: ${error.message}`);\n                } else if (data) {\n                    const successCount = data.filter((result)=>result.status === 'success').length;\n                    const errorCount = data.filter((result)=>result.status === 'error').length;\n                    setupResults.steps_completed.push(`Chunk ${i + 1}: ${successCount} statements executed`);\n                    if (errorCount > 0) {\n                        const errors = data.filter((result)=>result.status === 'error');\n                        errors.forEach((err)=>{\n                            setupResults.errors.push(`Chunk ${i + 1} error: ${err.message}`);\n                        });\n                    }\n                }\n            } catch (error) {\n                setupResults.errors.push(`Chunk ${i + 1} execution failed: ${error}`);\n            }\n        }\n        const isSuccess = setupResults.errors.length === 0;\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: isSuccess,\n            message: isSuccess ? '🎉 Database setup completed successfully!' : '⚠️ Database setup completed with some issues',\n            setup_results: setupResults,\n            next_steps: isSuccess ? [\n                '✅ Basic tables created successfully',\n                '🔄 Continue with media library tables',\n                '📱 Test the media library functionality',\n                '🚀 Verify bidirectional sync is working'\n            ] : [\n                '🔍 Check the errors above',\n                '🛠️ Some tables may need manual creation',\n                '📋 Consider running the full script manually',\n                '🔄 Try running the setup again'\n            ],\n            timestamp: new Date().toISOString()\n        });\n    } catch (error) {\n        console.error('[Setup Media DB] Setup failed:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            message: 'Automated database setup failed',\n            error: error instanceof Error ? error.message : 'Unknown error',\n            fallback_instructions: [\n                '📋 Use manual setup as fallback',\n                '1️⃣ Copy script from: docs/full-complete-supabase-script.md',\n                '2️⃣ Run in Supabase SQL Editor',\n                '3️⃣ Verify with GET /api/setup-media-db'\n            ],\n            timestamp: new Date().toISOString()\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9zZXR1cC1tZWRpYS1kYi9yb3V0ZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFBOzs7Ozs7Ozs7Ozs7Ozs7O0NBZ0JDLEdBRXlDO0FBQ3VCO0FBQ2I7QUFFcEQ7OztDQUdDLEdBQ00sZUFBZUc7SUFDcEIsSUFBSTtRQUNGQyxRQUFRQyxHQUFHLENBQUM7UUFFWixNQUFNQyxXQUFXSixtRUFBWUEsQ0FDM0JLLDBDQUFvQyxFQUNwQ0EsUUFBUUMsR0FBRyxDQUFDRSx5QkFBeUI7UUFHdkMsTUFBTUMsY0FBYztZQUNsQkMsb0JBQW9CO1lBQ3BCQyxjQUFjO1lBQ2RDLGlCQUFpQjtZQUNqQkMsZUFBZTtZQUNmQyxhQUFhO1lBQ2JDLGVBQWU7WUFDZkMsUUFBUSxFQUFFO1lBQ1ZDLFVBQVUsRUFBRTtRQUNkO1FBRUEsOEJBQThCO1FBQzlCLElBQUk7WUFDRixNQUFNLEVBQUVDLEtBQUssRUFBRSxHQUFHLE1BQU1kLFNBQVNlLElBQUksQ0FBQyxnQkFBZ0JDLE1BQU0sQ0FBQyxTQUFTQyxLQUFLLENBQUM7WUFDNUUsSUFBSUgsT0FBTyxNQUFNQTtZQUNqQlQsWUFBWUMsa0JBQWtCLEdBQUc7WUFDakNSLFFBQVFDLEdBQUcsQ0FBQztRQUNkLEVBQUUsT0FBT2UsT0FBTztZQUNkVCxZQUFZTyxNQUFNLENBQUNNLElBQUksQ0FBQyxDQUFDLDRCQUE0QixFQUFFSixPQUFPO1lBQzlEaEIsUUFBUWdCLEtBQUssQ0FBQyxrREFBa0RBO1FBQ2xFO1FBRUEsNkRBQTZEO1FBQzdELElBQUk7WUFDRixNQUFNSyxpQkFBaUI7Z0JBQUM7Z0JBQWdCO2dCQUFrQjtnQkFBZTtnQkFBcUI7YUFBeUI7WUFDdkgsTUFBTUMsaUJBQTJCLEVBQUU7WUFFbkMsS0FBSyxNQUFNQyxhQUFhRixlQUFnQjtnQkFDdEMsSUFBSTtvQkFDRixtQ0FBbUM7b0JBQ25DLE1BQU0sRUFBRUwsS0FBSyxFQUFFLEdBQUcsTUFBTWQsU0FDckJlLElBQUksQ0FBQ00sV0FDTEwsTUFBTSxDQUFDLFNBQ1BDLEtBQUssQ0FBQztvQkFFVCxJQUFJLENBQUNILE9BQU87d0JBQ1ZNLGVBQWVGLElBQUksQ0FBQ0c7b0JBQ3RCO2dCQUNGLEVBQUUsT0FBT0MsWUFBWTtvQkFDbkJ4QixRQUFReUIsSUFBSSxDQUFDLENBQUMsdUJBQXVCLEVBQUVGLFVBQVUsZUFBZSxDQUFDLEVBQUVDO2dCQUNyRTtZQUNGO1lBRUEsTUFBTUUsZ0JBQWdCTCxlQUFlTSxNQUFNLENBQUNDLENBQUFBLFFBQVMsQ0FBQ04sZUFBZU8sUUFBUSxDQUFDRDtZQUU5RSxJQUFJRixjQUFjSSxNQUFNLEtBQUssR0FBRztnQkFDOUJ2QixZQUFZRSxZQUFZLEdBQUc7Z0JBQzNCVCxRQUFRQyxHQUFHLENBQUM7WUFDZCxPQUFPO2dCQUNMTSxZQUFZTyxNQUFNLENBQUNNLElBQUksQ0FBQyxDQUFDLGdDQUFnQyxFQUFFTSxjQUFjSyxJQUFJLENBQUMsT0FBTztnQkFDckYvQixRQUFRZ0IsS0FBSyxDQUFDLHNDQUFzQ1U7WUFDdEQ7UUFDRixFQUFFLE9BQU9WLE9BQU87WUFDZFQsWUFBWU8sTUFBTSxDQUFDTSxJQUFJLENBQUMsQ0FBQywyQkFBMkIsRUFBRUosT0FBTztZQUM3RGhCLFFBQVFnQixLQUFLLENBQUMsaURBQWlEQTtRQUNqRTtRQUVBLHVDQUF1QztRQUN2QyxJQUFJO1lBQ0YsTUFBTSxFQUFFZ0IsTUFBTUMsU0FBUyxFQUFFakIsS0FBSyxFQUFFLEdBQUcsTUFBTWQsU0FDdENlLElBQUksQ0FBQywrQkFDTEMsTUFBTSxDQUFDLGdCQUNQZ0IsRUFBRSxDQUFDLGtCQUFrQixVQUNyQkMsRUFBRSxDQUFDLGdCQUFnQjtnQkFDbEI7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7YUFDRDtZQUVILElBQUluQixPQUFPLE1BQU1BO1lBRWpCLE1BQU1vQixvQkFBb0I7Z0JBQ3hCO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2FBQ0Q7WUFDRCxNQUFNQyxvQkFBb0JKLFdBQVdLLElBQUlDLENBQUFBLElBQUtBLEVBQUVDLFlBQVksS0FBSyxFQUFFO1lBQ25FLE1BQU1DLG1CQUFtQkwsa0JBQWtCVCxNQUFNLENBQUNlLENBQUFBLE9BQVEsQ0FBQ0wsa0JBQWtCUixRQUFRLENBQUNhO1lBRXRGLElBQUlELGlCQUFpQlgsTUFBTSxLQUFLLEdBQUc7Z0JBQ2pDdkIsWUFBWUcsZUFBZSxHQUFHO2dCQUM5QlYsUUFBUUMsR0FBRyxDQUFDO1lBQ2QsT0FBTztnQkFDTE0sWUFBWU8sTUFBTSxDQUFDTSxJQUFJLENBQUMsQ0FBQyxtQkFBbUIsRUFBRXFCLGlCQUFpQlYsSUFBSSxDQUFDLE9BQU87Z0JBQzNFL0IsUUFBUWdCLEtBQUssQ0FBQyx5Q0FBeUN5QjtZQUN6RDtRQUNGLEVBQUUsT0FBT3pCLE9BQU87WUFDZFQsWUFBWU8sTUFBTSxDQUFDTSxJQUFJLENBQUMsQ0FBQyw4QkFBOEIsRUFBRUosT0FBTztZQUNoRWhCLFFBQVFnQixLQUFLLENBQUMsb0RBQW9EQTtRQUNwRTtRQUVBLG1CQUFtQjtRQUNuQixJQUFJO1lBQ0YsTUFBTSxFQUFFZ0IsTUFBTVcsT0FBTyxFQUFFM0IsS0FBSyxFQUFFLEdBQUcsTUFBTWQsU0FDcENlLElBQUksQ0FBQyxjQUNMQyxNQUFNLENBQUMsYUFDUGdCLEVBQUUsQ0FBQyxjQUFjLFVBQ2pCVSxJQUFJLENBQUMsYUFBYTtZQUVyQixJQUFJNUIsT0FBTyxNQUFNQTtZQUVqQixNQUFNNkIsZUFBZUYsU0FBU2IsVUFBVTtZQUN4QyxJQUFJZSxnQkFBZ0IsSUFBSTtnQkFDdEJ0QyxZQUFZSSxhQUFhLEdBQUc7Z0JBQzVCWCxRQUFRQyxHQUFHLENBQUMsQ0FBQyx5QkFBeUIsRUFBRTRDLGFBQWEsY0FBYyxDQUFDO1lBQ3RFLE9BQU87Z0JBQ0x0QyxZQUFZUSxRQUFRLENBQUNLLElBQUksQ0FBQyxDQUFDLEtBQUssRUFBRXlCLGFBQWEsMENBQTBDLENBQUM7Z0JBQzFGN0MsUUFBUXlCLElBQUksQ0FBQyxDQUFDLHlCQUF5QixFQUFFb0IsYUFBYSxvQkFBb0IsQ0FBQztZQUM3RTtRQUNGLEVBQUUsT0FBTzdCLE9BQU87WUFDZFQsWUFBWVEsUUFBUSxDQUFDSyxJQUFJLENBQUMsQ0FBQywyQkFBMkIsRUFBRUosT0FBTztZQUMvRGhCLFFBQVF5QixJQUFJLENBQUMsa0RBQWtEVDtRQUNqRTtRQUVBLHVCQUF1QjtRQUN2QixJQUFJO1lBQ0YsZ0VBQWdFO1lBQ2hFLE1BQU0sRUFBRUEsS0FBSyxFQUFFLEdBQUcsTUFBTWQsU0FDckJlLElBQUksQ0FBQyxnQkFDTEMsTUFBTSxDQUFDLFNBQ1BDLEtBQUssQ0FBQztZQUVULElBQUksQ0FBQ0gsT0FBTztnQkFDVlQsWUFBWUssV0FBVyxHQUFHO2dCQUMxQlosUUFBUUMsR0FBRyxDQUFDO1lBQ2Q7UUFDRixFQUFFLE9BQU9lLE9BQU87WUFDZFQsWUFBWVEsUUFBUSxDQUFDSyxJQUFJLENBQUMsQ0FBQyx5QkFBeUIsRUFBRUosT0FBTztZQUM3RGhCLFFBQVF5QixJQUFJLENBQUMsZ0RBQWdEVDtRQUMvRDtRQUVBLDRCQUE0QjtRQUM1QixJQUFJO1lBQ0YsTUFBTThCLFFBQVEsTUFBTWpELDJFQUFvQkEsQ0FBQ2tELGFBQWE7WUFDdER4QyxZQUFZTSxhQUFhLEdBQUdpQztZQUM1QjlDLFFBQVFDLEdBQUcsQ0FBQyw0Q0FBNEM2QztRQUMxRCxFQUFFLE9BQU85QixPQUFPO1lBQ2RULFlBQVlRLFFBQVEsQ0FBQ0ssSUFBSSxDQUFDLENBQUMsNkJBQTZCLEVBQUVKLE9BQU87WUFDakVoQixRQUFReUIsSUFBSSxDQUFDLG9EQUFvRFQ7UUFDbkU7UUFFQSwyQkFBMkI7UUFDM0IsTUFBTWdDLGVBQWV6QyxZQUFZQyxrQkFBa0IsSUFDL0JELFlBQVlFLFlBQVksSUFDeEJGLFlBQVlHLGVBQWUsSUFDM0JILFlBQVlPLE1BQU0sQ0FBQ2dCLE1BQU0sS0FBSztRQUVsRCxNQUFNbUIsV0FBVztZQUNmQyxTQUFTRjtZQUNURyxTQUFTSCxlQUNMLG9GQUNBO1lBQ0pJLGNBQWM3QztZQUNkOEMsWUFBWUwsZUFBZTtnQkFDekI7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7YUFDRCxHQUFHO2dCQUNGO2dCQUNBO2dCQUNBO2dCQUNBO2FBQ0Q7WUFDRE0sbUJBQW1CO2dCQUNqQkMsY0FBYyxDQUFDLENBQUNwRCwwQ0FBb0M7Z0JBQ3BEcUQsc0JBQXNCLENBQUMsQ0FBQ3JELFFBQVFDLEdBQUcsQ0FBQ0UseUJBQXlCO2dCQUM3RG1ELHVCQUF1QixDQUFDLENBQUN0RCxXQUE2QztnQkFDdEV3RCxvQkFBb0IsQ0FBQyxDQUFDeEQsUUFBUUMsR0FBRyxDQUFDd0Qsa0JBQWtCO2dCQUNwREMsdUJBQXVCLENBQUMsQ0FBQzFELFFBQVFDLEdBQUcsQ0FBQzBELHFCQUFxQjtZQUM1RDtZQUNBQyxXQUFXLElBQUlDLE9BQU9DLFdBQVc7UUFDbkM7UUFFQWpFLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLHdEQUF3RCxFQUFFK0MsY0FBYztRQUVyRixPQUFPcEQscURBQVlBLENBQUNzRSxJQUFJLENBQUNqQjtJQUUzQixFQUFFLE9BQU9qQyxPQUFPO1FBQ2RoQixRQUFRZ0IsS0FBSyxDQUFDLCtDQUErQ0E7UUFFN0QsT0FBT3BCLHFEQUFZQSxDQUFDc0UsSUFBSSxDQUFDO1lBQ3ZCaEIsU0FBUztZQUNUbEMsT0FBTztZQUNQbUMsU0FBU25DLGlCQUFpQm1ELFFBQVFuRCxNQUFNbUMsT0FBTyxHQUFHO1lBQ2xEWSxXQUFXLElBQUlDLE9BQU9DLFdBQVc7UUFDbkMsR0FBRztZQUFFRyxRQUFRO1FBQUk7SUFDbkI7QUFDRjtBQUVBOzs7Q0FHQyxHQUNNLGVBQWVDO0lBQ3BCLElBQUk7UUFDRnJFLFFBQVFDLEdBQUcsQ0FBQztRQUVaLE1BQU1DLFdBQVdKLG1FQUFZQSxDQUMzQkssMENBQW9DLEVBQ3BDQSxRQUFRQyxHQUFHLENBQUNFLHlCQUF5QjtRQUd2QyxNQUFNZ0UsZUFBZTtZQUNuQkMsaUJBQWlCLEVBQUU7WUFDbkJ6RCxRQUFRLEVBQUU7WUFDVkMsVUFBVSxFQUFFO1FBQ2Q7UUFFQSx3REFBd0Q7UUFDeEQsSUFBSTtZQUNGZixRQUFRQyxHQUFHLENBQUM7WUFFWixNQUFNdUUsbUJBQW1CLENBQUM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7TUFtQjFCLENBQUM7WUFFRCxNQUFNLEVBQUV4RCxPQUFPeUQsYUFBYSxFQUFFLEdBQUcsTUFBTXZFLFNBQVN3RSxHQUFHLENBQUMsUUFBUTtnQkFBRUMsS0FBS0g7WUFBaUI7WUFFcEYsSUFBSUMsZUFBZTtnQkFDakIsc0RBQXNEO2dCQUN0RCxNQUFNLEVBQUV6RCxPQUFPNEQsV0FBVyxFQUFFLEdBQUcsTUFBTTFFLFNBQ2xDZSxJQUFJLENBQUMsV0FDTEMsTUFBTSxDQUFDLFdBQ1BnQixFQUFFLENBQUMsV0FBVyxxQkFDZDJDLE1BQU07Z0JBRVQsSUFBSUQsYUFBYTtvQkFDZk4sYUFBYXZELFFBQVEsQ0FBQ0ssSUFBSSxDQUFDO2dCQUM3QjtZQUNGO1lBRUFrRCxhQUFhQyxlQUFlLENBQUNuRCxJQUFJLENBQUM7UUFDcEMsRUFBRSxPQUFPSixPQUFPO1lBQ2RzRCxhQUFheEQsTUFBTSxDQUFDTSxJQUFJLENBQUMsQ0FBQyxnQ0FBZ0MsRUFBRUosT0FBTztRQUNyRTtRQUVBLDJDQUEyQztRQUMzQyxNQUFNOEQsWUFBWTtZQUNoQixrQ0FBa0M7WUFDbEM7Z0JBQ0UsQ0FBQyxtSUFBbUksQ0FBQztnQkFDckksQ0FBQyxvSkFBb0osQ0FBQztnQkFDdEosQ0FBQyxvSUFBb0ksQ0FBQztnQkFDdEksQ0FBQyxrSkFBa0osQ0FBQztnQkFDcEosQ0FBQzs7Ozs7Ozs7Ozs7VUFXQyxDQUFDO2FBQ0o7WUFDRCw0QkFBNEI7WUFDNUI7Z0JBQ0UsQ0FBQzs7Ozs7Ozs7Ozs7Ozs7Ozs7OztVQW1CQyxDQUFDO2dCQUNILENBQUM7Ozs7Ozs7Ozs7VUFVQyxDQUFDO2FBQ0o7U0FDRjtRQUVELHFCQUFxQjtRQUNyQixJQUFLLElBQUlDLElBQUksR0FBR0EsSUFBSUQsVUFBVWhELE1BQU0sRUFBRWlELElBQUs7WUFDekMsSUFBSTtnQkFDRi9FLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLGlDQUFpQyxFQUFFOEUsSUFBSSxFQUFFLEdBQUcsQ0FBQztnQkFFMUQsTUFBTSxFQUFFL0MsSUFBSSxFQUFFaEIsS0FBSyxFQUFFLEdBQUcsTUFBTWQsU0FBU3dFLEdBQUcsQ0FBQyxxQkFBcUI7b0JBQzlETSxnQkFBZ0JGLFNBQVMsQ0FBQ0MsRUFBRTtnQkFDOUI7Z0JBRUEsSUFBSS9ELE9BQU87b0JBQ1RzRCxhQUFheEQsTUFBTSxDQUFDTSxJQUFJLENBQUMsQ0FBQyxNQUFNLEVBQUUyRCxJQUFJLEVBQUUsU0FBUyxFQUFFL0QsTUFBTW1DLE9BQU8sRUFBRTtnQkFDcEUsT0FBTyxJQUFJbkIsTUFBTTtvQkFDZixNQUFNaUQsZUFBZWpELEtBQUtMLE1BQU0sQ0FBQyxDQUFDdUQsU0FBK0JBLE9BQU9kLE1BQU0sS0FBSyxXQUFXdEMsTUFBTTtvQkFDcEcsTUFBTXFELGFBQWFuRCxLQUFLTCxNQUFNLENBQUMsQ0FBQ3VELFNBQStCQSxPQUFPZCxNQUFNLEtBQUssU0FBU3RDLE1BQU07b0JBRWhHd0MsYUFBYUMsZUFBZSxDQUFDbkQsSUFBSSxDQUFDLENBQUMsTUFBTSxFQUFFMkQsSUFBSSxFQUFFLEVBQUUsRUFBRUUsYUFBYSxvQkFBb0IsQ0FBQztvQkFFdkYsSUFBSUUsYUFBYSxHQUFHO3dCQUNsQixNQUFNckUsU0FBU2tCLEtBQUtMLE1BQU0sQ0FBQyxDQUFDdUQsU0FBK0JBLE9BQU9kLE1BQU0sS0FBSzt3QkFDN0V0RCxPQUFPc0UsT0FBTyxDQUFDLENBQUNDOzRCQUNkZixhQUFheEQsTUFBTSxDQUFDTSxJQUFJLENBQUMsQ0FBQyxNQUFNLEVBQUUyRCxJQUFJLEVBQUUsUUFBUSxFQUFFTSxJQUFJbEMsT0FBTyxFQUFFO3dCQUNqRTtvQkFDRjtnQkFDRjtZQUNGLEVBQUUsT0FBT25DLE9BQU87Z0JBQ2RzRCxhQUFheEQsTUFBTSxDQUFDTSxJQUFJLENBQUMsQ0FBQyxNQUFNLEVBQUUyRCxJQUFJLEVBQUUsbUJBQW1CLEVBQUUvRCxPQUFPO1lBQ3RFO1FBQ0Y7UUFFQSxNQUFNc0UsWUFBWWhCLGFBQWF4RCxNQUFNLENBQUNnQixNQUFNLEtBQUs7UUFFakQsT0FBT2xDLHFEQUFZQSxDQUFDc0UsSUFBSSxDQUFDO1lBQ3ZCaEIsU0FBU29DO1lBQ1RuQyxTQUFTbUMsWUFDTCw4Q0FDQTtZQUNKQyxlQUFlakI7WUFDZmpCLFlBQVlpQyxZQUFZO2dCQUN0QjtnQkFDQTtnQkFDQTtnQkFDQTthQUNELEdBQUc7Z0JBQ0Y7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7YUFDRDtZQUNEdkIsV0FBVyxJQUFJQyxPQUFPQyxXQUFXO1FBQ25DO0lBRUYsRUFBRSxPQUFPakQsT0FBTztRQUNkaEIsUUFBUWdCLEtBQUssQ0FBQyxrQ0FBa0NBO1FBRWhELE9BQU9wQixxREFBWUEsQ0FBQ3NFLElBQUksQ0FBQztZQUN2QmhCLFNBQVM7WUFDVEMsU0FBUztZQUNUbkMsT0FBT0EsaUJBQWlCbUQsUUFBUW5ELE1BQU1tQyxPQUFPLEdBQUc7WUFDaERxQyx1QkFBdUI7Z0JBQ3JCO2dCQUNBO2dCQUNBO2dCQUNBO2FBQ0Q7WUFDRHpCLFdBQVcsSUFBSUMsT0FBT0MsV0FBVztRQUNuQyxHQUFHO1lBQUVHLFFBQVE7UUFBSTtJQUNuQjtBQUNGIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXERBQkJJRVxcT25lRHJpdmVcXERlc2t0b3BcXGxndS1wcm9qZWN0LWFwcFxcc3JjXFxhcHBcXGFwaVxcc2V0dXAtbWVkaWEtZGJcXHJvdXRlLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogU2V0dXAgTWVkaWEgRGF0YWJhc2UgQVBJIFJvdXRlXG4gKiBcbiAqIFZlcmlmaWVzIGFuZCBpbml0aWFsaXplcyB0aGUgbWVkaWEgbGlicmFyeSBkYXRhYmFzZSBzZXR1cC5cbiAqIEVuc3VyZXMgYWxsIHRhYmxlcywgZnVuY3Rpb25zLCBhbmQgY29uZmlndXJhdGlvbnMgYXJlIHByb3Blcmx5IHNldCB1cFxuICogZm9yIGJpZGlyZWN0aW9uYWwgc3luYyB3aXRoIENsb3VkaW5hcnkuXG4gKiBcbiAqIEZlYXR1cmVzOlxuICogLSDwn5SNIERhdGFiYXNlIHNjaGVtYSB2ZXJpZmljYXRpb25cbiAqIC0g8J+boO+4jyBGdW5jdGlvbiBhdmFpbGFiaWxpdHkgY2hlY2tzXG4gKiAtIPCfk4ogSW5pdGlhbCBzdGF0aXN0aWNzIGdhdGhlcmluZ1xuICogLSDwn5SEIFN5bmMgc3RhdHVzIHZhbGlkYXRpb25cbiAqIC0g8J+agCBSZWFkeS10by11c2UgY29uZmlybWF0aW9uXG4gKiBcbiAqIEBhdXRob3IgTEdVIFByb2plY3QgVGVhbVxuICogQHZlcnNpb24gMS4wLjBcbiAqL1xuXG5pbXBvcnQgeyBOZXh0UmVzcG9uc2UgfSBmcm9tICduZXh0L3NlcnZlcidcbmltcG9ydCB7IFN1cGFiYXNlTWVkaWFTZXJ2aWNlIH0gZnJvbSAnQC9saWIvc3VwYWJhc2VNZWRpYVNlcnZpY2UnXG5pbXBvcnQgeyBjcmVhdGVDbGllbnQgfSBmcm9tICdAc3VwYWJhc2Uvc3VwYWJhc2UtanMnXG5cbi8qKlxuICogR0VUIC9hcGkvc2V0dXAtbWVkaWEtZGJcbiAqIFZlcmlmeSBtZWRpYSBkYXRhYmFzZSBzZXR1cCBhbmQgY29uZmlndXJhdGlvblxuICovXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gR0VUKCkge1xuICB0cnkge1xuICAgIGNvbnNvbGUubG9nKCdbU2V0dXAgTWVkaWEgREJdIFZlcmlmeWluZyBkYXRhYmFzZSBzZXR1cC4uLicpXG5cbiAgICBjb25zdCBzdXBhYmFzZSA9IGNyZWF0ZUNsaWVudChcbiAgICAgIHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1NVUEFCQVNFX1VSTCEsXG4gICAgICBwcm9jZXNzLmVudi5TVVBBQkFTRV9TRVJWSUNFX1JPTEVfS0VZIVxuICAgIClcblxuICAgIGNvbnN0IHNldHVwU3RhdHVzID0ge1xuICAgICAgZGF0YWJhc2VfY29ubmVjdGVkOiBmYWxzZSxcbiAgICAgIHRhYmxlc19leGlzdDogZmFsc2UsXG4gICAgICBmdW5jdGlvbnNfZXhpc3Q6IGZhbHNlLFxuICAgICAgaW5kZXhlc19leGlzdDogZmFsc2UsXG4gICAgICBybHNfZW5hYmxlZDogZmFsc2UsXG4gICAgICBpbml0aWFsX3N0YXRzOiBudWxsIGFzIFJlY29yZDxzdHJpbmcsIHVua25vd24+IHwgbnVsbCxcbiAgICAgIGVycm9yczogW10gYXMgc3RyaW5nW10sXG4gICAgICB3YXJuaW5nczogW10gYXMgc3RyaW5nW11cbiAgICB9XG5cbiAgICAvLyAxLiBUZXN0IGRhdGFiYXNlIGNvbm5lY3Rpb25cbiAgICB0cnkge1xuICAgICAgY29uc3QgeyBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2UuZnJvbSgnbWVkaWFfYXNzZXRzJykuc2VsZWN0KCdjb3VudCcpLmxpbWl0KDEpXG4gICAgICBpZiAoZXJyb3IpIHRocm93IGVycm9yXG4gICAgICBzZXR1cFN0YXR1cy5kYXRhYmFzZV9jb25uZWN0ZWQgPSB0cnVlXG4gICAgICBjb25zb2xlLmxvZygnW1NldHVwIE1lZGlhIERCXSDinIUgRGF0YWJhc2UgY29ubmVjdGlvbiBzdWNjZXNzZnVsJylcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgc2V0dXBTdGF0dXMuZXJyb3JzLnB1c2goYERhdGFiYXNlIGNvbm5lY3Rpb24gZmFpbGVkOiAke2Vycm9yfWApXG4gICAgICBjb25zb2xlLmVycm9yKCdbU2V0dXAgTWVkaWEgREJdIOKdjCBEYXRhYmFzZSBjb25uZWN0aW9uIGZhaWxlZDonLCBlcnJvcilcbiAgICB9XG5cbiAgICAvLyAyLiBDaGVjayBpZiByZXF1aXJlZCB0YWJsZXMgZXhpc3QgYnkgdGVzdGluZyBkaXJlY3QgYWNjZXNzXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlcXVpcmVkVGFibGVzID0gWydtZWRpYV9hc3NldHMnLCAnbWVkaWFfc3luY19sb2cnLCAnbWVkaWFfdXNhZ2UnLCAnbWVkaWFfY29sbGVjdGlvbnMnLCAnbWVkaWFfY29sbGVjdGlvbl9pdGVtcyddXG4gICAgICBjb25zdCBleGlzdGluZ1RhYmxlczogc3RyaW5nW10gPSBbXVxuXG4gICAgICBmb3IgKGNvbnN0IHRhYmxlTmFtZSBvZiByZXF1aXJlZFRhYmxlcykge1xuICAgICAgICB0cnkge1xuICAgICAgICAgIC8vIFRlc3QgZGlyZWN0IGFjY2VzcyB0byBlYWNoIHRhYmxlXG4gICAgICAgICAgY29uc3QgeyBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgICAgICAgIC5mcm9tKHRhYmxlTmFtZSlcbiAgICAgICAgICAgIC5zZWxlY3QoJ2NvdW50JylcbiAgICAgICAgICAgIC5saW1pdCgxKVxuXG4gICAgICAgICAgaWYgKCFlcnJvcikge1xuICAgICAgICAgICAgZXhpc3RpbmdUYWJsZXMucHVzaCh0YWJsZU5hbWUpXG4gICAgICAgICAgfVxuICAgICAgICB9IGNhdGNoICh0YWJsZUVycm9yKSB7XG4gICAgICAgICAgY29uc29sZS53YXJuKGBbU2V0dXAgTWVkaWEgREJdIFRhYmxlICR7dGFibGVOYW1lfSBhY2Nlc3MgZmFpbGVkOmAsIHRhYmxlRXJyb3IpXG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgY29uc3QgbWlzc2luZ1RhYmxlcyA9IHJlcXVpcmVkVGFibGVzLmZpbHRlcih0YWJsZSA9PiAhZXhpc3RpbmdUYWJsZXMuaW5jbHVkZXModGFibGUpKVxuXG4gICAgICBpZiAobWlzc2luZ1RhYmxlcy5sZW5ndGggPT09IDApIHtcbiAgICAgICAgc2V0dXBTdGF0dXMudGFibGVzX2V4aXN0ID0gdHJ1ZVxuICAgICAgICBjb25zb2xlLmxvZygnW1NldHVwIE1lZGlhIERCXSDinIUgQWxsIHJlcXVpcmVkIHRhYmxlcyBleGlzdCBhbmQgYXJlIGFjY2Vzc2libGUnKVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgc2V0dXBTdGF0dXMuZXJyb3JzLnB1c2goYE1pc3Npbmcgb3IgaW5hY2Nlc3NpYmxlIHRhYmxlczogJHttaXNzaW5nVGFibGVzLmpvaW4oJywgJyl9YClcbiAgICAgICAgY29uc29sZS5lcnJvcignW1NldHVwIE1lZGlhIERCXSDinYwgTWlzc2luZyB0YWJsZXM6JywgbWlzc2luZ1RhYmxlcylcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgc2V0dXBTdGF0dXMuZXJyb3JzLnB1c2goYFRhYmxlIHZlcmlmaWNhdGlvbiBmYWlsZWQ6ICR7ZXJyb3J9YClcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ1tTZXR1cCBNZWRpYSBEQl0g4p2MIFRhYmxlIHZlcmlmaWNhdGlvbiBmYWlsZWQ6JywgZXJyb3IpXG4gICAgfVxuXG4gICAgLy8gMy4gQ2hlY2sgaWYgcmVxdWlyZWQgZnVuY3Rpb25zIGV4aXN0XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHsgZGF0YTogZnVuY3Rpb25zLCBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgICAgLmZyb20oJ2luZm9ybWF0aW9uX3NjaGVtYS5yb3V0aW5lcycpXG4gICAgICAgIC5zZWxlY3QoJ3JvdXRpbmVfbmFtZScpXG4gICAgICAgIC5lcSgncm91dGluZV9zY2hlbWEnLCAncHVibGljJylcbiAgICAgICAgLmluKCdyb3V0aW5lX25hbWUnLCBbXG4gICAgICAgICAgJ3NvZnRfZGVsZXRlX21lZGlhX2Fzc2V0JyxcbiAgICAgICAgICAncmVzdG9yZV9tZWRpYV9hc3NldCcsXG4gICAgICAgICAgJ3VwZGF0ZV9tZWRpYV9zeW5jX3N0YXR1cycsXG4gICAgICAgICAgJ2dldF9tZWRpYV9zdGF0aXN0aWNzJyxcbiAgICAgICAgICAnY2xlYW51cF9vbGRfc3luY19sb2dzJ1xuICAgICAgICBdKVxuXG4gICAgICBpZiAoZXJyb3IpIHRocm93IGVycm9yXG5cbiAgICAgIGNvbnN0IHJlcXVpcmVkRnVuY3Rpb25zID0gW1xuICAgICAgICAnc29mdF9kZWxldGVfbWVkaWFfYXNzZXQnLFxuICAgICAgICAncmVzdG9yZV9tZWRpYV9hc3NldCcsIFxuICAgICAgICAndXBkYXRlX21lZGlhX3N5bmNfc3RhdHVzJyxcbiAgICAgICAgJ2dldF9tZWRpYV9zdGF0aXN0aWNzJyxcbiAgICAgICAgJ2NsZWFudXBfb2xkX3N5bmNfbG9ncydcbiAgICAgIF1cbiAgICAgIGNvbnN0IGV4aXN0aW5nRnVuY3Rpb25zID0gZnVuY3Rpb25zPy5tYXAoZiA9PiBmLnJvdXRpbmVfbmFtZSkgfHwgW11cbiAgICAgIGNvbnN0IG1pc3NpbmdGdW5jdGlvbnMgPSByZXF1aXJlZEZ1bmN0aW9ucy5maWx0ZXIoZnVuYyA9PiAhZXhpc3RpbmdGdW5jdGlvbnMuaW5jbHVkZXMoZnVuYykpXG5cbiAgICAgIGlmIChtaXNzaW5nRnVuY3Rpb25zLmxlbmd0aCA9PT0gMCkge1xuICAgICAgICBzZXR1cFN0YXR1cy5mdW5jdGlvbnNfZXhpc3QgPSB0cnVlXG4gICAgICAgIGNvbnNvbGUubG9nKCdbU2V0dXAgTWVkaWEgREJdIOKchSBBbGwgcmVxdWlyZWQgZnVuY3Rpb25zIGV4aXN0JylcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHNldHVwU3RhdHVzLmVycm9ycy5wdXNoKGBNaXNzaW5nIGZ1bmN0aW9uczogJHttaXNzaW5nRnVuY3Rpb25zLmpvaW4oJywgJyl9YClcbiAgICAgICAgY29uc29sZS5lcnJvcignW1NldHVwIE1lZGlhIERCXSDinYwgTWlzc2luZyBmdW5jdGlvbnM6JywgbWlzc2luZ0Z1bmN0aW9ucylcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgc2V0dXBTdGF0dXMuZXJyb3JzLnB1c2goYEZ1bmN0aW9uIHZlcmlmaWNhdGlvbiBmYWlsZWQ6ICR7ZXJyb3J9YClcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ1tTZXR1cCBNZWRpYSBEQl0g4p2MIEZ1bmN0aW9uIHZlcmlmaWNhdGlvbiBmYWlsZWQ6JywgZXJyb3IpXG4gICAgfVxuXG4gICAgLy8gNC4gQ2hlY2sgaW5kZXhlc1xuICAgIHRyeSB7XG4gICAgICBjb25zdCB7IGRhdGE6IGluZGV4ZXMsIGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgICAuZnJvbSgncGdfaW5kZXhlcycpXG4gICAgICAgIC5zZWxlY3QoJ2luZGV4bmFtZScpXG4gICAgICAgIC5lcSgnc2NoZW1hbmFtZScsICdwdWJsaWMnKVxuICAgICAgICAubGlrZSgnaW5kZXhuYW1lJywgJ2lkeF9tZWRpYV8lJylcblxuICAgICAgaWYgKGVycm9yKSB0aHJvdyBlcnJvclxuXG4gICAgICBjb25zdCBtZWRpYUluZGV4ZXMgPSBpbmRleGVzPy5sZW5ndGggfHwgMFxuICAgICAgaWYgKG1lZGlhSW5kZXhlcyA+PSAxMCkgeyAvLyBXZSBleHBlY3QgYXQgbGVhc3QgMTAgbWVkaWEtcmVsYXRlZCBpbmRleGVzXG4gICAgICAgIHNldHVwU3RhdHVzLmluZGV4ZXNfZXhpc3QgPSB0cnVlXG4gICAgICAgIGNvbnNvbGUubG9nKGBbU2V0dXAgTWVkaWEgREJdIOKchSBGb3VuZCAke21lZGlhSW5kZXhlc30gbWVkaWEgaW5kZXhlc2ApXG4gICAgICB9IGVsc2Uge1xuICAgICAgICBzZXR1cFN0YXR1cy53YXJuaW5ncy5wdXNoKGBPbmx5ICR7bWVkaWFJbmRleGVzfSBtZWRpYSBpbmRleGVzIGZvdW5kLCBleHBlY3RlZCBhdCBsZWFzdCAxMGApXG4gICAgICAgIGNvbnNvbGUud2FybihgW1NldHVwIE1lZGlhIERCXSDimqDvuI8gT25seSAke21lZGlhSW5kZXhlc30gbWVkaWEgaW5kZXhlcyBmb3VuZGApXG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIHNldHVwU3RhdHVzLndhcm5pbmdzLnB1c2goYEluZGV4IHZlcmlmaWNhdGlvbiBmYWlsZWQ6ICR7ZXJyb3J9YClcbiAgICAgIGNvbnNvbGUud2FybignW1NldHVwIE1lZGlhIERCXSDimqDvuI8gSW5kZXggdmVyaWZpY2F0aW9uIGZhaWxlZDonLCBlcnJvcilcbiAgICB9XG5cbiAgICAvLyA1LiBUZXN0IFJMUyBwb2xpY2llc1xuICAgIHRyeSB7XG4gICAgICAvLyBUcnkgdG8gYWNjZXNzIG1lZGlhX2Fzc2V0cyB3aXRoIGF1dGhlbnRpY2F0ZWQgcm9sZSBzaW11bGF0aW9uXG4gICAgICBjb25zdCB7IGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgICAuZnJvbSgnbWVkaWFfYXNzZXRzJylcbiAgICAgICAgLnNlbGVjdCgnY291bnQnKVxuICAgICAgICAubGltaXQoMSlcblxuICAgICAgaWYgKCFlcnJvcikge1xuICAgICAgICBzZXR1cFN0YXR1cy5ybHNfZW5hYmxlZCA9IHRydWVcbiAgICAgICAgY29uc29sZS5sb2coJ1tTZXR1cCBNZWRpYSBEQl0g4pyFIFJMUyBwb2xpY2llcyB3b3JraW5nJylcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgc2V0dXBTdGF0dXMud2FybmluZ3MucHVzaChgUkxTIHZlcmlmaWNhdGlvbiBmYWlsZWQ6ICR7ZXJyb3J9YClcbiAgICAgIGNvbnNvbGUud2FybignW1NldHVwIE1lZGlhIERCXSDimqDvuI8gUkxTIHZlcmlmaWNhdGlvbiBmYWlsZWQ6JywgZXJyb3IpXG4gICAgfVxuXG4gICAgLy8gNi4gR2V0IGluaXRpYWwgc3RhdGlzdGljc1xuICAgIHRyeSB7XG4gICAgICBjb25zdCBzdGF0cyA9IGF3YWl0IFN1cGFiYXNlTWVkaWFTZXJ2aWNlLmdldE1lZGlhU3RhdHMoKVxuICAgICAgc2V0dXBTdGF0dXMuaW5pdGlhbF9zdGF0cyA9IHN0YXRzIGFzIHVua25vd24gYXMgUmVjb3JkPHN0cmluZywgdW5rbm93bj5cbiAgICAgIGNvbnNvbGUubG9nKCdbU2V0dXAgTWVkaWEgREJdIOKchSBTdGF0aXN0aWNzIHJldHJpZXZlZDonLCBzdGF0cylcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgc2V0dXBTdGF0dXMud2FybmluZ3MucHVzaChgU3RhdGlzdGljcyByZXRyaWV2YWwgZmFpbGVkOiAke2Vycm9yfWApXG4gICAgICBjb25zb2xlLndhcm4oJ1tTZXR1cCBNZWRpYSBEQl0g4pqg77iPIFN0YXRpc3RpY3MgcmV0cmlldmFsIGZhaWxlZDonLCBlcnJvcilcbiAgICB9XG5cbiAgICAvLyBEZXRlcm1pbmUgb3ZlcmFsbCBzdGF0dXNcbiAgICBjb25zdCBpc0Z1bGx5U2V0dXAgPSBzZXR1cFN0YXR1cy5kYXRhYmFzZV9jb25uZWN0ZWQgJiYgXG4gICAgICAgICAgICAgICAgICAgICAgICBzZXR1cFN0YXR1cy50YWJsZXNfZXhpc3QgJiYgXG4gICAgICAgICAgICAgICAgICAgICAgICBzZXR1cFN0YXR1cy5mdW5jdGlvbnNfZXhpc3QgJiZcbiAgICAgICAgICAgICAgICAgICAgICAgIHNldHVwU3RhdHVzLmVycm9ycy5sZW5ndGggPT09IDBcblxuICAgIGNvbnN0IHJlc3BvbnNlID0ge1xuICAgICAgc3VjY2VzczogaXNGdWxseVNldHVwLFxuICAgICAgbWVzc2FnZTogaXNGdWxseVNldHVwIFxuICAgICAgICA/ICfwn46JIE1lZGlhIGxpYnJhcnkgZGF0YWJhc2UgaXMgZnVsbHkgY29uZmlndXJlZCBhbmQgcmVhZHkgZm9yIGJpZGlyZWN0aW9uYWwgc3luYyEnXG4gICAgICAgIDogJ+KaoO+4jyBNZWRpYSBsaWJyYXJ5IGRhdGFiYXNlIHNldHVwIGhhcyBpc3N1ZXMgdGhhdCBuZWVkIGF0dGVudGlvbicsXG4gICAgICBzZXR1cF9zdGF0dXM6IHNldHVwU3RhdHVzLFxuICAgICAgbmV4dF9zdGVwczogaXNGdWxseVNldHVwID8gW1xuICAgICAgICAn4pyFIERhdGFiYXNlIGlzIHJlYWR5IGZvciBwcm9kdWN0aW9uIHVzZScsXG4gICAgICAgICfwn5SEIFRlc3QgdGhlIHN5bmMgZnVuY3Rpb25hbGl0eSBpbiBhZG1pbiBwYW5lbCcsXG4gICAgICAgICfwn5OxIENvbmZpZ3VyZSBDbG91ZGluYXJ5IHdlYmhvb2tzIGZvciByZWFsLXRpbWUgc3luYycsXG4gICAgICAgICfwn5qAIFN0YXJ0IHVwbG9hZGluZyBhbmQgbWFuYWdpbmcgbWVkaWEgZmlsZXMnXG4gICAgICBdIDogW1xuICAgICAgICAn8J+TiyBSdW4gdGhlIGNvbXBsZXRlIFN1cGFiYXNlIHNjcmlwdCBmcm9tIGRvY3MvZnVsbC1jb21wbGV0ZS1zdXBhYmFzZS1zY3JpcHQubWQnLFxuICAgICAgICAn8J+UjSBDaGVjayBkYXRhYmFzZSBjb25uZWN0aW9uIGFuZCBwZXJtaXNzaW9ucycsXG4gICAgICAgICfwn5ug77iPIFZlcmlmeSBhbGwgZW52aXJvbm1lbnQgdmFyaWFibGVzIGFyZSBzZXQgY29ycmVjdGx5JyxcbiAgICAgICAgJ/CflIQgUmUtcnVuIHRoaXMgc2V0dXAgdmVyaWZpY2F0aW9uJ1xuICAgICAgXSxcbiAgICAgIGVudmlyb25tZW50X2NoZWNrOiB7XG4gICAgICAgIHN1cGFiYXNlX3VybDogISFwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19TVVBBQkFTRV9VUkwsXG4gICAgICAgIHN1cGFiYXNlX3NlcnZpY2Vfa2V5OiAhIXByb2Nlc3MuZW52LlNVUEFCQVNFX1NFUlZJQ0VfUk9MRV9LRVksXG4gICAgICAgIGNsb3VkaW5hcnlfY2xvdWRfbmFtZTogISFwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19DTE9VRElOQVJZX0NMT1VEX05BTUUsXG4gICAgICAgIGNsb3VkaW5hcnlfYXBpX2tleTogISFwcm9jZXNzLmVudi5DTE9VRElOQVJZX0FQSV9LRVksXG4gICAgICAgIGNsb3VkaW5hcnlfYXBpX3NlY3JldDogISFwcm9jZXNzLmVudi5DTE9VRElOQVJZX0FQSV9TRUNSRVRcbiAgICAgIH0sXG4gICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgIH1cblxuICAgIGNvbnNvbGUubG9nKGBbU2V0dXAgTWVkaWEgREJdIFNldHVwIHZlcmlmaWNhdGlvbiBjb21wbGV0ZWQuIFN1Y2Nlc3M6ICR7aXNGdWxseVNldHVwfWApXG4gICAgXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHJlc3BvbnNlKVxuXG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignW1NldHVwIE1lZGlhIERCXSBTZXR1cCB2ZXJpZmljYXRpb24gZmFpbGVkOicsIGVycm9yKVxuICAgIFxuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7XG4gICAgICBzdWNjZXNzOiBmYWxzZSxcbiAgICAgIGVycm9yOiAnU2V0dXAgdmVyaWZpY2F0aW9uIGZhaWxlZCcsXG4gICAgICBtZXNzYWdlOiBlcnJvciBpbnN0YW5jZW9mIEVycm9yID8gZXJyb3IubWVzc2FnZSA6ICdVbmtub3duIGVycm9yJyxcbiAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpXG4gICAgfSwgeyBzdGF0dXM6IDUwMCB9KVxuICB9XG59XG5cbi8qKlxuICogUE9TVCAvYXBpL3NldHVwLW1lZGlhLWRiXG4gKiBJbml0aWFsaXplIG9yIHJlcGFpciBtZWRpYSBkYXRhYmFzZSBzZXR1cCB1c2luZyBTdXBhYmFzZSBSUENcbiAqL1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIFBPU1QoKSB7XG4gIHRyeSB7XG4gICAgY29uc29sZS5sb2coJ1tTZXR1cCBNZWRpYSBEQl0gU3RhcnRpbmcgYXV0b21hdGVkIGRhdGFiYXNlIHNldHVwLi4uJylcblxuICAgIGNvbnN0IHN1cGFiYXNlID0gY3JlYXRlQ2xpZW50KFxuICAgICAgcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfU1VQQUJBU0VfVVJMISxcbiAgICAgIHByb2Nlc3MuZW52LlNVUEFCQVNFX1NFUlZJQ0VfUk9MRV9LRVkhXG4gICAgKVxuXG4gICAgY29uc3Qgc2V0dXBSZXN1bHRzID0ge1xuICAgICAgc3RlcHNfY29tcGxldGVkOiBbXSBhcyBzdHJpbmdbXSxcbiAgICAgIGVycm9yczogW10gYXMgc3RyaW5nW10sXG4gICAgICB3YXJuaW5nczogW10gYXMgc3RyaW5nW11cbiAgICB9XG5cbiAgICAvLyBTdGVwIDE6IENyZWF0ZSB0aGUgc2V0dXAgZnVuY3Rpb24gaWYgaXQgZG9lc24ndCBleGlzdFxuICAgIHRyeSB7XG4gICAgICBjb25zb2xlLmxvZygnW1NldHVwIE1lZGlhIERCXSBDcmVhdGluZyBzZXR1cCBmdW5jdGlvbi4uLicpXG5cbiAgICAgIGNvbnN0IHNldHVwRnVuY3Rpb25TUUwgPSBgXG4gICAgICAgIENSRUFURSBPUiBSRVBMQUNFIEZVTkNUSU9OIGV4ZWN1dGVfc2V0dXBfc3FsKHNxbF9zdGF0ZW1lbnRzIFRFWFRbXSlcbiAgICAgICAgUkVUVVJOUyBUQUJMRShzdGVwX251bWJlciBJTlRFR0VSLCBzdGF0dXMgVEVYVCwgbWVzc2FnZSBURVhUKSBBUyAkJFxuICAgICAgICBERUNMQVJFXG4gICAgICAgICAgICBzdG10IFRFWFQ7XG4gICAgICAgICAgICBzdGVwX251bSBJTlRFR0VSIDo9IDE7XG4gICAgICAgIEJFR0lOXG4gICAgICAgICAgICBGT1JFQUNIIHN0bXQgSU4gQVJSQVkgc3FsX3N0YXRlbWVudHNcbiAgICAgICAgICAgIExPT1BcbiAgICAgICAgICAgICAgICBCRUdJTlxuICAgICAgICAgICAgICAgICAgICBFWEVDVVRFIHN0bXQ7XG4gICAgICAgICAgICAgICAgICAgIFJFVFVSTiBRVUVSWSBTRUxFQ1Qgc3RlcF9udW0sICdzdWNjZXNzJzo6VEVYVCwgKCdFeGVjdXRlZDogJyB8fCBMRUZUKHN0bXQsIDUwKSB8fCAnLi4uJyk6OlRFWFQ7XG4gICAgICAgICAgICAgICAgRVhDRVBUSU9OIFdIRU4gT1RIRVJTIFRIRU5cbiAgICAgICAgICAgICAgICAgICAgUkVUVVJOIFFVRVJZIFNFTEVDVCBzdGVwX251bSwgJ2Vycm9yJzo6VEVYVCwgKFNRTEVSUk0gfHwgJyAtIFNRTDogJyB8fCBMRUZUKHN0bXQsIDUwKSB8fCAnLi4uJyk6OlRFWFQ7XG4gICAgICAgICAgICAgICAgRU5EO1xuICAgICAgICAgICAgICAgIHN0ZXBfbnVtIDo9IHN0ZXBfbnVtICsgMTtcbiAgICAgICAgICAgIEVORCBMT09QO1xuICAgICAgICBFTkQ7XG4gICAgICAgICQkIExBTkdVQUdFIHBscGdzcWwgU0VDVVJJVFkgREVGSU5FUjtcbiAgICAgIGBcblxuICAgICAgY29uc3QgeyBlcnJvcjogZnVuY3Rpb25FcnJvciB9ID0gYXdhaXQgc3VwYWJhc2UucnBjKCdleGVjJywgeyBzcWw6IHNldHVwRnVuY3Rpb25TUUwgfSlcblxuICAgICAgaWYgKGZ1bmN0aW9uRXJyb3IpIHtcbiAgICAgICAgLy8gVHJ5IGFsdGVybmF0aXZlIGFwcHJvYWNoIC0gY3JlYXRlIGZ1bmN0aW9uIGRpcmVjdGx5XG4gICAgICAgIGNvbnN0IHsgZXJyb3I6IGRpcmVjdEVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgICAgIC5mcm9tKCdwZ19wcm9jJylcbiAgICAgICAgICAuc2VsZWN0KCdwcm9uYW1lJylcbiAgICAgICAgICAuZXEoJ3Byb25hbWUnLCAnZXhlY3V0ZV9zZXR1cF9zcWwnKVxuICAgICAgICAgIC5zaW5nbGUoKVxuXG4gICAgICAgIGlmIChkaXJlY3RFcnJvcikge1xuICAgICAgICAgIHNldHVwUmVzdWx0cy53YXJuaW5ncy5wdXNoKCdDb3VsZCBub3QgdmVyaWZ5IHNldHVwIGZ1bmN0aW9uIGNyZWF0aW9uJylcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICBzZXR1cFJlc3VsdHMuc3RlcHNfY29tcGxldGVkLnB1c2goJ1NldHVwIGZ1bmN0aW9uIGNyZWF0ZWQnKVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBzZXR1cFJlc3VsdHMuZXJyb3JzLnB1c2goYFNldHVwIGZ1bmN0aW9uIGNyZWF0aW9uIGZhaWxlZDogJHtlcnJvcn1gKVxuICAgIH1cblxuICAgIC8vIFN0ZXAgMjogRXhlY3V0ZSBkYXRhYmFzZSBzZXR1cCBpbiBjaHVua3NcbiAgICBjb25zdCBzcWxDaHVua3MgPSBbXG4gICAgICAvLyBDaHVuayAxOiBUeXBlcyBhbmQgYmFzaWMgdGFibGVzXG4gICAgICBbXG4gICAgICAgIGBETyAkJCBCRUdJTiBDUkVBVEUgVFlQRSB1c2VyX3N0YXR1cyBBUyBFTlVNICgnQUNUSVZFJywgJ0lOQUNUSVZFJywgJ1NVU1BFTkRFRCcpOyBFWENFUFRJT04gV0hFTiBkdXBsaWNhdGVfb2JqZWN0IFRIRU4gbnVsbDsgRU5EICQkO2AsXG4gICAgICAgIGBETyAkJCBCRUdJTiBDUkVBVEUgVFlQRSBwZXJzb25uZWxfc3RhdHVzIEFTIEVOVU0gKCdBY3RpdmUnLCAnSW5hY3RpdmUnLCAnT24gTGVhdmUnLCAnU3VzcGVuZGVkJyk7IEVYQ0VQVElPTiBXSEVOIGR1cGxpY2F0ZV9vYmplY3QgVEhFTiBudWxsOyBFTkQgJCQ7YCxcbiAgICAgICAgYERPICQkIEJFR0lOIENSRUFURSBUWVBFIG1lZGlhX3N5bmNfc3RhdHVzIEFTIEVOVU0gKCdzeW5jZWQnLCAncGVuZGluZycsICdlcnJvcicpOyBFWENFUFRJT04gV0hFTiBkdXBsaWNhdGVfb2JqZWN0IFRIRU4gbnVsbDsgRU5EICQkO2AsXG4gICAgICAgIGBETyAkJCBCRUdJTiBDUkVBVEUgVFlQRSBtZWRpYV9zeW5jX29wZXJhdGlvbiBBUyBFTlVNICgndXBsb2FkJywgJ2RlbGV0ZScsICd1cGRhdGUnLCAncmVzdG9yZScpOyBFWENFUFRJT04gV0hFTiBkdXBsaWNhdGVfb2JqZWN0IFRIRU4gbnVsbDsgRU5EICQkO2AsXG4gICAgICAgIGBDUkVBVEUgVEFCTEUgSUYgTk9UIEVYSVNUUyB1c2VycyAoXG4gICAgICAgICAgaWQgQklHU0VSSUFMIFBSSU1BUlkgS0VZLFxuICAgICAgICAgIGVtYWlsIFZBUkNIQVIoMjU1KSBVTklRVUUgTk9UIE5VTEwsXG4gICAgICAgICAgbmFtZSBWQVJDSEFSKDI1NSkgTk9UIE5VTEwsXG4gICAgICAgICAgcGFzc3dvcmQgVkFSQ0hBUigyNTUpIE5PVCBOVUxMLFxuICAgICAgICAgIHBob25lIFZBUkNIQVIoNTApLFxuICAgICAgICAgIGFkZHJlc3MgVEVYVCxcbiAgICAgICAgICByb2xlIFZBUkNIQVIoNTApIERFRkFVTFQgJ3VzZXInLFxuICAgICAgICAgIHN0YXR1cyB1c2VyX3N0YXR1cyBERUZBVUxUICdBQ1RJVkUnLFxuICAgICAgICAgIGNyZWF0ZWRfYXQgVElNRVNUQU1QIFdJVEggVElNRSBaT05FIERFRkFVTFQgTk9XKCksXG4gICAgICAgICAgdXBkYXRlZF9hdCBUSU1FU1RBTVAgV0lUSCBUSU1FIFpPTkUgREVGQVVMVCBOT1coKVxuICAgICAgICApO2BcbiAgICAgIF0sXG4gICAgICAvLyBDaHVuayAyOiBQZXJzb25uZWwgdGFibGVzXG4gICAgICBbXG4gICAgICAgIGBDUkVBVEUgVEFCTEUgSUYgTk9UIEVYSVNUUyBwZXJzb25uZWwgKFxuICAgICAgICAgIGlkIEJJR1NFUklBTCBQUklNQVJZIEtFWSxcbiAgICAgICAgICBuYW1lIFZBUkNIQVIoMjU1KSBOT1QgTlVMTCxcbiAgICAgICAgICBlbWFpbCBWQVJDSEFSKDI1NSkgVU5JUVVFIE5PVCBOVUxMLFxuICAgICAgICAgIHBob25lIFZBUkNIQVIoNTApLFxuICAgICAgICAgIGFkZHJlc3MgVEVYVCxcbiAgICAgICAgICBwcm9maWxlX3Bob3RvIFZBUkNIQVIoNTAwKSxcbiAgICAgICAgICBkZXBhcnRtZW50IFZBUkNIQVIoMjU1KSBOT1QgTlVMTCxcbiAgICAgICAgICBwb3NpdGlvbiBWQVJDSEFSKDI1NSksXG4gICAgICAgICAgaGlyZV9kYXRlIERBVEUsXG4gICAgICAgICAgc3RhdHVzIHBlcnNvbm5lbF9zdGF0dXMgREVGQVVMVCAnQWN0aXZlJyxcbiAgICAgICAgICBiaW9ncmFwaHkgVEVYVCxcbiAgICAgICAgICBzcG91c2VfbmFtZSBWQVJDSEFSKDI1NSksXG4gICAgICAgICAgc3BvdXNlX29jY3VwYXRpb24gVkFSQ0hBUigyNTUpLFxuICAgICAgICAgIGNoaWxkcmVuX2NvdW50IFZBUkNIQVIoMTApLFxuICAgICAgICAgIGVtZXJnZW5jeV9jb250YWN0IFZBUkNIQVIoNTApLFxuICAgICAgICAgIGNoaWxkcmVuX25hbWVzIFRFWFQsXG4gICAgICAgICAgY3JlYXRlZF9hdCBUSU1FU1RBTVAgV0lUSCBUSU1FIFpPTkUgREVGQVVMVCBOT1coKSxcbiAgICAgICAgICB1cGRhdGVkX2F0IFRJTUVTVEFNUCBXSVRIIFRJTUUgWk9ORSBERUZBVUxUIE5PVygpXG4gICAgICAgICk7YCxcbiAgICAgICAgYENSRUFURSBUQUJMRSBJRiBOT1QgRVhJU1RTIHBlcnNvbm5lbF9kb2N1bWVudHMgKFxuICAgICAgICAgIGlkIEJJR1NFUklBTCBQUklNQVJZIEtFWSxcbiAgICAgICAgICBmaWxlbmFtZSBWQVJDSEFSKDI1NSkgTk9UIE5VTEwsXG4gICAgICAgICAgb3JpZ2luYWxfbmFtZSBWQVJDSEFSKDI1NSkgTk9UIE5VTEwsXG4gICAgICAgICAgbWltZV90eXBlIFZBUkNIQVIoMTAwKSBOT1QgTlVMTCxcbiAgICAgICAgICBzaXplIElOVEVHRVIgTk9UIE5VTEwsXG4gICAgICAgICAgcGF0aCBWQVJDSEFSKDUwMCkgTk9UIE5VTEwsXG4gICAgICAgICAgcGVyc29ubmVsX2lkIEJJR0lOVCBOT1QgTlVMTCBSRUZFUkVOQ0VTIHBlcnNvbm5lbChpZCkgT04gREVMRVRFIENBU0NBREUsXG4gICAgICAgICAgY3JlYXRlZF9hdCBUSU1FU1RBTVAgV0lUSCBUSU1FIFpPTkUgREVGQVVMVCBOT1coKSxcbiAgICAgICAgICB1cGRhdGVkX2F0IFRJTUVTVEFNUCBXSVRIIFRJTUUgWk9ORSBERUZBVUxUIE5PVygpXG4gICAgICAgICk7YFxuICAgICAgXVxuICAgIF1cblxuICAgIC8vIEV4ZWN1dGUgZWFjaCBjaHVua1xuICAgIGZvciAobGV0IGkgPSAwOyBpIDwgc3FsQ2h1bmtzLmxlbmd0aDsgaSsrKSB7XG4gICAgICB0cnkge1xuICAgICAgICBjb25zb2xlLmxvZyhgW1NldHVwIE1lZGlhIERCXSBFeGVjdXRpbmcgY2h1bmsgJHtpICsgMX0uLi5gKVxuXG4gICAgICAgIGNvbnN0IHsgZGF0YSwgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlLnJwYygnZXhlY3V0ZV9zZXR1cF9zcWwnLCB7XG4gICAgICAgICAgc3FsX3N0YXRlbWVudHM6IHNxbENodW5rc1tpXVxuICAgICAgICB9KVxuXG4gICAgICAgIGlmIChlcnJvcikge1xuICAgICAgICAgIHNldHVwUmVzdWx0cy5lcnJvcnMucHVzaChgQ2h1bmsgJHtpICsgMX0gZmFpbGVkOiAke2Vycm9yLm1lc3NhZ2V9YClcbiAgICAgICAgfSBlbHNlIGlmIChkYXRhKSB7XG4gICAgICAgICAgY29uc3Qgc3VjY2Vzc0NvdW50ID0gZGF0YS5maWx0ZXIoKHJlc3VsdDogeyBzdGF0dXM6IHN0cmluZyB9KSA9PiByZXN1bHQuc3RhdHVzID09PSAnc3VjY2VzcycpLmxlbmd0aFxuICAgICAgICAgIGNvbnN0IGVycm9yQ291bnQgPSBkYXRhLmZpbHRlcigocmVzdWx0OiB7IHN0YXR1czogc3RyaW5nIH0pID0+IHJlc3VsdC5zdGF0dXMgPT09ICdlcnJvcicpLmxlbmd0aFxuXG4gICAgICAgICAgc2V0dXBSZXN1bHRzLnN0ZXBzX2NvbXBsZXRlZC5wdXNoKGBDaHVuayAke2kgKyAxfTogJHtzdWNjZXNzQ291bnR9IHN0YXRlbWVudHMgZXhlY3V0ZWRgKVxuXG4gICAgICAgICAgaWYgKGVycm9yQ291bnQgPiAwKSB7XG4gICAgICAgICAgICBjb25zdCBlcnJvcnMgPSBkYXRhLmZpbHRlcigocmVzdWx0OiB7IHN0YXR1czogc3RyaW5nIH0pID0+IHJlc3VsdC5zdGF0dXMgPT09ICdlcnJvcicpXG4gICAgICAgICAgICBlcnJvcnMuZm9yRWFjaCgoZXJyOiB7IG1lc3NhZ2U6IHN0cmluZyB9KSA9PiB7XG4gICAgICAgICAgICAgIHNldHVwUmVzdWx0cy5lcnJvcnMucHVzaChgQ2h1bmsgJHtpICsgMX0gZXJyb3I6ICR7ZXJyLm1lc3NhZ2V9YClcbiAgICAgICAgICAgIH0pXG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBzZXR1cFJlc3VsdHMuZXJyb3JzLnB1c2goYENodW5rICR7aSArIDF9IGV4ZWN1dGlvbiBmYWlsZWQ6ICR7ZXJyb3J9YClcbiAgICAgIH1cbiAgICB9XG5cbiAgICBjb25zdCBpc1N1Y2Nlc3MgPSBzZXR1cFJlc3VsdHMuZXJyb3JzLmxlbmd0aCA9PT0gMFxuXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHtcbiAgICAgIHN1Y2Nlc3M6IGlzU3VjY2VzcyxcbiAgICAgIG1lc3NhZ2U6IGlzU3VjY2Vzc1xuICAgICAgICA/ICfwn46JIERhdGFiYXNlIHNldHVwIGNvbXBsZXRlZCBzdWNjZXNzZnVsbHkhJ1xuICAgICAgICA6ICfimqDvuI8gRGF0YWJhc2Ugc2V0dXAgY29tcGxldGVkIHdpdGggc29tZSBpc3N1ZXMnLFxuICAgICAgc2V0dXBfcmVzdWx0czogc2V0dXBSZXN1bHRzLFxuICAgICAgbmV4dF9zdGVwczogaXNTdWNjZXNzID8gW1xuICAgICAgICAn4pyFIEJhc2ljIHRhYmxlcyBjcmVhdGVkIHN1Y2Nlc3NmdWxseScsXG4gICAgICAgICfwn5SEIENvbnRpbnVlIHdpdGggbWVkaWEgbGlicmFyeSB0YWJsZXMnLFxuICAgICAgICAn8J+TsSBUZXN0IHRoZSBtZWRpYSBsaWJyYXJ5IGZ1bmN0aW9uYWxpdHknLFxuICAgICAgICAn8J+agCBWZXJpZnkgYmlkaXJlY3Rpb25hbCBzeW5jIGlzIHdvcmtpbmcnXG4gICAgICBdIDogW1xuICAgICAgICAn8J+UjSBDaGVjayB0aGUgZXJyb3JzIGFib3ZlJyxcbiAgICAgICAgJ/Cfm6DvuI8gU29tZSB0YWJsZXMgbWF5IG5lZWQgbWFudWFsIGNyZWF0aW9uJyxcbiAgICAgICAgJ/Cfk4sgQ29uc2lkZXIgcnVubmluZyB0aGUgZnVsbCBzY3JpcHQgbWFudWFsbHknLFxuICAgICAgICAn8J+UhCBUcnkgcnVubmluZyB0aGUgc2V0dXAgYWdhaW4nXG4gICAgICBdLFxuICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKClcbiAgICB9KVxuXG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignW1NldHVwIE1lZGlhIERCXSBTZXR1cCBmYWlsZWQ6JywgZXJyb3IpXG5cbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oe1xuICAgICAgc3VjY2VzczogZmFsc2UsXG4gICAgICBtZXNzYWdlOiAnQXV0b21hdGVkIGRhdGFiYXNlIHNldHVwIGZhaWxlZCcsXG4gICAgICBlcnJvcjogZXJyb3IgaW5zdGFuY2VvZiBFcnJvciA/IGVycm9yLm1lc3NhZ2UgOiAnVW5rbm93biBlcnJvcicsXG4gICAgICBmYWxsYmFja19pbnN0cnVjdGlvbnM6IFtcbiAgICAgICAgJ/Cfk4sgVXNlIG1hbnVhbCBzZXR1cCBhcyBmYWxsYmFjaycsXG4gICAgICAgICcx77iP4oOjIENvcHkgc2NyaXB0IGZyb206IGRvY3MvZnVsbC1jb21wbGV0ZS1zdXBhYmFzZS1zY3JpcHQubWQnLFxuICAgICAgICAnMu+4j+KDoyBSdW4gaW4gU3VwYWJhc2UgU1FMIEVkaXRvcicsXG4gICAgICAgICcz77iP4oOjIFZlcmlmeSB3aXRoIEdFVCAvYXBpL3NldHVwLW1lZGlhLWRiJ1xuICAgICAgXSxcbiAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpXG4gICAgfSwgeyBzdGF0dXM6IDUwMCB9KVxuICB9XG59XG4iXSwibmFtZXMiOlsiTmV4dFJlc3BvbnNlIiwiU3VwYWJhc2VNZWRpYVNlcnZpY2UiLCJjcmVhdGVDbGllbnQiLCJHRVQiLCJjb25zb2xlIiwibG9nIiwic3VwYWJhc2UiLCJwcm9jZXNzIiwiZW52IiwiTkVYVF9QVUJMSUNfU1VQQUJBU0VfVVJMIiwiU1VQQUJBU0VfU0VSVklDRV9ST0xFX0tFWSIsInNldHVwU3RhdHVzIiwiZGF0YWJhc2VfY29ubmVjdGVkIiwidGFibGVzX2V4aXN0IiwiZnVuY3Rpb25zX2V4aXN0IiwiaW5kZXhlc19leGlzdCIsInJsc19lbmFibGVkIiwiaW5pdGlhbF9zdGF0cyIsImVycm9ycyIsIndhcm5pbmdzIiwiZXJyb3IiLCJmcm9tIiwic2VsZWN0IiwibGltaXQiLCJwdXNoIiwicmVxdWlyZWRUYWJsZXMiLCJleGlzdGluZ1RhYmxlcyIsInRhYmxlTmFtZSIsInRhYmxlRXJyb3IiLCJ3YXJuIiwibWlzc2luZ1RhYmxlcyIsImZpbHRlciIsInRhYmxlIiwiaW5jbHVkZXMiLCJsZW5ndGgiLCJqb2luIiwiZGF0YSIsImZ1bmN0aW9ucyIsImVxIiwiaW4iLCJyZXF1aXJlZEZ1bmN0aW9ucyIsImV4aXN0aW5nRnVuY3Rpb25zIiwibWFwIiwiZiIsInJvdXRpbmVfbmFtZSIsIm1pc3NpbmdGdW5jdGlvbnMiLCJmdW5jIiwiaW5kZXhlcyIsImxpa2UiLCJtZWRpYUluZGV4ZXMiLCJzdGF0cyIsImdldE1lZGlhU3RhdHMiLCJpc0Z1bGx5U2V0dXAiLCJyZXNwb25zZSIsInN1Y2Nlc3MiLCJtZXNzYWdlIiwic2V0dXBfc3RhdHVzIiwibmV4dF9zdGVwcyIsImVudmlyb25tZW50X2NoZWNrIiwic3VwYWJhc2VfdXJsIiwic3VwYWJhc2Vfc2VydmljZV9rZXkiLCJjbG91ZGluYXJ5X2Nsb3VkX25hbWUiLCJORVhUX1BVQkxJQ19DTE9VRElOQVJZX0NMT1VEX05BTUUiLCJjbG91ZGluYXJ5X2FwaV9rZXkiLCJDTE9VRElOQVJZX0FQSV9LRVkiLCJjbG91ZGluYXJ5X2FwaV9zZWNyZXQiLCJDTE9VRElOQVJZX0FQSV9TRUNSRVQiLCJ0aW1lc3RhbXAiLCJEYXRlIiwidG9JU09TdHJpbmciLCJqc29uIiwiRXJyb3IiLCJzdGF0dXMiLCJQT1NUIiwic2V0dXBSZXN1bHRzIiwic3RlcHNfY29tcGxldGVkIiwic2V0dXBGdW5jdGlvblNRTCIsImZ1bmN0aW9uRXJyb3IiLCJycGMiLCJzcWwiLCJkaXJlY3RFcnJvciIsInNpbmdsZSIsInNxbENodW5rcyIsImkiLCJzcWxfc3RhdGVtZW50cyIsInN1Y2Nlc3NDb3VudCIsInJlc3VsdCIsImVycm9yQ291bnQiLCJmb3JFYWNoIiwiZXJyIiwiaXNTdWNjZXNzIiwic2V0dXBfcmVzdWx0cyIsImZhbGxiYWNrX2luc3RydWN0aW9ucyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/setup-media-db/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabaseMediaService.ts":
/*!*****************************************!*\
  !*** ./src/lib/supabaseMediaService.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SupabaseMediaService: () => (/* binding */ SupabaseMediaService)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/**\n * Supabase Media Service\n * \n * Enterprise-grade database service for media library with perfect bidirectional sync.\n * Handles all database operations for media assets with Supabase integration.\n * \n * Features:\n * - 🗄️ Complete CRUD operations for media assets\n * - 🔄 Sync status management\n * - 📊 Real-time statistics\n * - 🔍 Advanced search and filtering\n * - 📝 Audit trail logging\n * - 🔒 Enterprise-grade error handling\n * - ⚡ Optimized queries with indexes\n * \n * <AUTHOR> Project Team\n * @version 1.0.0\n */ \n/**\n * Supabase Media Service Class\n */ class SupabaseMediaService {\n    static{\n        this.supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(\"https://lkolpgpmdculqqfqyzaf.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY);\n    }\n    /**\n   * Create or update media asset in database\n   */ static async upsertMediaAsset(asset) {\n        try {\n            console.log('[SupabaseMediaService] Upserting media asset:', asset.cloudinary_public_id);\n            const { data, error } = await this.supabase.from('media_assets').upsert({\n                ...asset,\n                updated_at: new Date().toISOString(),\n                last_synced_at: new Date().toISOString()\n            }, {\n                onConflict: 'cloudinary_public_id'\n            }).select().single();\n            if (error) {\n                throw new Error(`Failed to upsert media asset: ${error.message}`);\n            }\n            return data;\n        } catch (error) {\n            console.error('[SupabaseMediaService] Upsert failed:', error);\n            throw error;\n        }\n    }\n    /**\n   * Get media asset by Cloudinary public ID\n   */ static async getMediaAssetByPublicId(publicId) {\n        try {\n            const { data, error } = await this.supabase.from('media_assets').select('*').eq('cloudinary_public_id', publicId).eq('deleted_at', null).single();\n            if (error && error.code !== 'PGRST116') {\n                throw new Error(`Failed to get media asset: ${error.message}`);\n            }\n            return data;\n        } catch (error) {\n            console.error('[SupabaseMediaService] Get asset failed:', error);\n            return null;\n        }\n    }\n    /**\n   * Search media assets with advanced filtering\n   */ static async searchMediaAssets(options = {}) {\n        try {\n            const { search, folder, resource_type, tags, sync_status, uploaded_by, date_from, date_to, min_size, max_size, page = 1, limit = 50, sort_by = 'created_at', sort_order = 'desc' } = options;\n            let query = this.supabase.from('media_assets').select('*', {\n                count: 'exact'\n            }).is('deleted_at', null);\n            // Apply filters\n            if (search) {\n                query = query.or(`original_filename.ilike.%${search}%,display_name.ilike.%${search}%,description.ilike.%${search}%`);\n            }\n            if (folder) {\n                query = query.eq('folder', folder);\n            }\n            if (resource_type) {\n                query = query.eq('resource_type', resource_type);\n            }\n            if (tags && tags.length > 0) {\n                query = query.overlaps('tags', tags);\n            }\n            if (sync_status) {\n                query = query.eq('sync_status', sync_status);\n            }\n            if (uploaded_by) {\n                query = query.eq('uploaded_by', uploaded_by);\n            }\n            if (date_from) {\n                query = query.gte('created_at', date_from);\n            }\n            if (date_to) {\n                query = query.lte('created_at', date_to);\n            }\n            if (min_size) {\n                query = query.gte('file_size', min_size);\n            }\n            if (max_size) {\n                query = query.lte('file_size', max_size);\n            }\n            // Apply sorting and pagination\n            const offset = (page - 1) * limit;\n            query = query.order(sort_by, {\n                ascending: sort_order === 'asc'\n            }).range(offset, offset + limit - 1);\n            const { data, error, count } = await query;\n            if (error) {\n                throw new Error(`Failed to search media assets: ${error.message}`);\n            }\n            const total = count || 0;\n            const totalPages = Math.ceil(total / limit);\n            return {\n                assets: data,\n                total,\n                page,\n                limit,\n                has_next: page < totalPages,\n                has_prev: page > 1\n            };\n        } catch (error) {\n            console.error('[SupabaseMediaService] Search failed:', error);\n            throw error;\n        }\n    }\n    /**\n   * Get media statistics\n   */ static async getMediaStats() {\n        try {\n            // Try the function first\n            const { data: functionData, error: functionError } = await this.supabase.rpc('get_media_statistics');\n            if (!functionError && functionData) {\n                return functionData[0];\n            }\n            // Fallback: Calculate stats manually\n            console.log('[SupabaseMediaService] Function not available, calculating stats manually...');\n            const { data: assets, error } = await this.supabase.from('media_assets').select('resource_type, file_size, sync_status').is('deleted_at', null);\n            if (error) {\n                console.error('[SupabaseMediaService] Failed to get media assets for stats:', error);\n                throw error;\n            }\n            const stats = {\n                total_assets: assets?.length || 0,\n                total_images: assets?.filter((a)=>a.resource_type === 'image').length || 0,\n                total_videos: assets?.filter((a)=>a.resource_type === 'video').length || 0,\n                total_raw: assets?.filter((a)=>a.resource_type === 'raw').length || 0,\n                total_size: assets?.reduce((sum, a)=>sum + (a.file_size || 0), 0) || 0,\n                synced_assets: assets?.filter((a)=>a.sync_status === 'synced').length || 0,\n                pending_assets: assets?.filter((a)=>a.sync_status === 'pending').length || 0,\n                error_assets: assets?.filter((a)=>a.sync_status === 'error').length || 0\n            };\n            return stats;\n        } catch (error) {\n            console.error('[SupabaseMediaService] Get stats failed:', error);\n            // Return default stats on error\n            return {\n                total_assets: 0,\n                total_images: 0,\n                total_videos: 0,\n                total_raw: 0,\n                total_size: 0,\n                synced_assets: 0,\n                pending_assets: 0,\n                error_assets: 0\n            };\n        }\n    }\n    /**\n   * Soft delete media asset\n   */ static async softDeleteMediaAsset(publicId, deletedBy) {\n        try {\n            console.log('[SupabaseMediaService] Soft deleting media asset:', publicId);\n            const { data, error } = await this.supabase.rpc('soft_delete_media_asset', {\n                asset_id: publicId,\n                deleted_by_user: deletedBy\n            });\n            if (error) {\n                throw new Error(`Failed to soft delete media asset: ${error.message}`);\n            }\n            return data;\n        } catch (error) {\n            console.error('[SupabaseMediaService] Soft delete failed:', error);\n            throw error;\n        }\n    }\n    /**\n   * Update sync status\n   */ static async updateSyncStatus(publicId, status, errorMessage) {\n        try {\n            const { data, error } = await this.supabase.rpc('update_media_sync_status', {\n                asset_id: publicId,\n                new_status: status,\n                error_msg: errorMessage\n            });\n            if (error) {\n                throw new Error(`Failed to update sync status: ${error.message}`);\n            }\n            return data;\n        } catch (error) {\n            console.error('[SupabaseMediaService] Update sync status failed:', error);\n            throw error;\n        }\n    }\n    /**\n   * Log sync operation\n   */ static async logSyncOperation(log) {\n        try {\n            const { data, error } = await this.supabase.from('media_sync_log').insert({\n                ...log,\n                created_at: new Date().toISOString()\n            }).select().single();\n            if (error) {\n                throw new Error(`Failed to log sync operation: ${error.message}`);\n            }\n            return data;\n        } catch (error) {\n            console.error('[SupabaseMediaService] Log sync operation failed:', error);\n            throw error;\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabaseMediaService.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fsetup-media-db%2Froute&page=%2Fapi%2Fsetup-media-db%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsetup-media-db%2Froute.ts&appDir=C%3A%5CUsers%5CDABBIE%5COneDrive%5CDesktop%5Clgu-project-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDABBIE%5COneDrive%5CDesktop%5Clgu-project-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();