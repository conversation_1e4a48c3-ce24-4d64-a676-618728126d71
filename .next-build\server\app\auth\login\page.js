/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/auth/login/page";
exports.ids = ["app/auth/login/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fauth%2Flogin%2Fpage&page=%2Fauth%2Flogin%2Fpage&appPaths=%2Fauth%2Flogin%2Fpage&pagePath=private-next-app-dir%2Fauth%2Flogin%2Fpage.tsx&appDir=C%3A%5CUsers%5CDABBIE%5COneDrive%5CDesktop%5Clgu-project-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDABBIE%5COneDrive%5CDesktop%5Clgu-project-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fauth%2Flogin%2Fpage&page=%2Fauth%2Flogin%2Fpage&appPaths=%2Fauth%2Flogin%2Fpage&pagePath=private-next-app-dir%2Fauth%2Flogin%2Fpage.tsx&appDir=C%3A%5CUsers%5CDABBIE%5COneDrive%5CDesktop%5Clgu-project-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDABBIE%5COneDrive%5CDesktop%5Clgu-project-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/auth/login/page.tsx */ \"(rsc)/./src/app/auth/login/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'auth',\n        {\n        children: [\n        'login',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/auth/login/page\",\n        pathname: \"/auth/login\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZhdXRoJTJGbG9naW4lMkZwYWdlJnBhZ2U9JTJGYXV0aCUyRmxvZ2luJTJGcGFnZSZhcHBQYXRocz0lMkZhdXRoJTJGbG9naW4lMkZwYWdlJnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGYXV0aCUyRmxvZ2luJTJGcGFnZS50c3gmYXBwRGlyPUMlM0ElNUNVc2VycyU1Q0RBQkJJRSU1Q09uZURyaXZlJTVDRGVza3RvcCU1Q2xndS1wcm9qZWN0LWFwcCU1Q3NyYyU1Q2FwcCZwYWdlRXh0ZW5zaW9ucz10c3gmcGFnZUV4dGVuc2lvbnM9dHMmcGFnZUV4dGVuc2lvbnM9anN4JnBhZ2VFeHRlbnNpb25zPWpzJnJvb3REaXI9QyUzQSU1Q1VzZXJzJTVDREFCQklFJTVDT25lRHJpdmUlNUNEZXNrdG9wJTVDbGd1LXByb2plY3QtYXBwJmlzRGV2PXRydWUmdHNjb25maWdQYXRoPXRzY29uZmlnLmpzb24mYmFzZVBhdGg9JmFzc2V0UHJlZml4PSZuZXh0Q29uZmlnT3V0cHV0PSZwcmVmZXJyZWRSZWdpb249Jm1pZGRsZXdhcmVDb25maWc9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSxzQkFBc0Isb0pBQWdIO0FBQ3RJLHNCQUFzQiwwTkFBZ0Y7QUFDdEcsc0JBQXNCLDBOQUFnRjtBQUN0RyxzQkFBc0IsZ09BQW1GO0FBQ3pHLG9CQUFvQixzS0FBMkg7QUFHN0k7QUFHQTtBQUNGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlDQUFpQztBQUNqQztBQUNBO0FBQ0EsU0FBUztBQUNULE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBLG9DQUFvQyxzZkFBd1E7QUFDNVM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0NBQW9DLHNmQUF3UTtBQUM1UztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUN1QjtBQUdyQjtBQUNGLDZCQUE2QixtQkFBbUI7QUFDaEQ7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUdFO0FBQ0Y7QUFDTyx3QkFBd0IsdUdBQWtCO0FBQ2pEO0FBQ0EsY0FBYyxrRUFBUztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0EsQ0FBQzs7QUFFRCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IG1vZHVsZTAgPSAoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXERBQkJJRVxcXFxPbmVEcml2ZVxcXFxEZXNrdG9wXFxcXGxndS1wcm9qZWN0LWFwcFxcXFxzcmNcXFxcYXBwXFxcXGxheW91dC50c3hcIik7XG5jb25zdCBtb2R1bGUxID0gKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvbm90LWZvdW5kLWVycm9yXCIpO1xuY29uc3QgbW9kdWxlMiA9ICgpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2ZvcmJpZGRlbi1lcnJvclwiKTtcbmNvbnN0IG1vZHVsZTMgPSAoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy91bmF1dGhvcml6ZWQtZXJyb3JcIik7XG5jb25zdCBwYWdlNCA9ICgpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcREFCQklFXFxcXE9uZURyaXZlXFxcXERlc2t0b3BcXFxcbGd1LXByb2plY3QtYXBwXFxcXHNyY1xcXFxhcHBcXFxcYXV0aFxcXFxsb2dpblxcXFxwYWdlLnRzeFwiKTtcbmltcG9ydCB7IEFwcFBhZ2VSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLW1vZHVsZXMvYXBwLXBhZ2UvbW9kdWxlLmNvbXBpbGVkXCIgd2l0aCB7XG4gICAgJ3R1cmJvcGFjay10cmFuc2l0aW9uJzogJ25leHQtc3NyJ1xufTtcbmltcG9ydCB7IFJvdXRlS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLWtpbmRcIiB3aXRoIHtcbiAgICAndHVyYm9wYWNrLXRyYW5zaXRpb24nOiAnbmV4dC1zZXJ2ZXItdXRpbGl0eSdcbn07XG4vLyBXZSBpbmplY3QgdGhlIHRyZWUgYW5kIHBhZ2VzIGhlcmUgc28gdGhhdCB3ZSBjYW4gdXNlIHRoZW0gaW4gdGhlIHJvdXRlXG4vLyBtb2R1bGUuXG5jb25zdCB0cmVlID0ge1xuICAgICAgICBjaGlsZHJlbjogW1xuICAgICAgICAnJyxcbiAgICAgICAge1xuICAgICAgICBjaGlsZHJlbjogW1xuICAgICAgICAnYXV0aCcsXG4gICAgICAgIHtcbiAgICAgICAgY2hpbGRyZW46IFtcbiAgICAgICAgJ2xvZ2luJyxcbiAgICAgICAge1xuICAgICAgICBjaGlsZHJlbjogWydfX1BBR0VfXycsIHt9LCB7XG4gICAgICAgICAgcGFnZTogW3BhZ2U0LCBcIkM6XFxcXFVzZXJzXFxcXERBQkJJRVxcXFxPbmVEcml2ZVxcXFxEZXNrdG9wXFxcXGxndS1wcm9qZWN0LWFwcFxcXFxzcmNcXFxcYXBwXFxcXGF1dGhcXFxcbG9naW5cXFxccGFnZS50c3hcIl0sXG4gICAgICAgICAgXG4gICAgICAgIH1dXG4gICAgICB9LFxuICAgICAgICB7XG4gICAgICAgIFxuICAgICAgICBcbiAgICAgIH1cbiAgICAgIF1cbiAgICAgIH0sXG4gICAgICAgIHtcbiAgICAgICAgXG4gICAgICAgIG1ldGFkYXRhOiB7XG4gICAgaWNvbjogWyhhc3luYyAocHJvcHMpID0+IChhd2FpdCBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIm5leHQtbWV0YWRhdGEtaW1hZ2UtbG9hZGVyP3R5cGU9ZmF2aWNvbiZzZWdtZW50PSZiYXNlUGF0aD0mcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyFDOlxcXFxVc2Vyc1xcXFxEQUJCSUVcXFxcT25lRHJpdmVcXFxcRGVza3RvcFxcXFxsZ3UtcHJvamVjdC1hcHBcXFxcc3JjXFxcXGFwcFxcXFxmYXZpY29uLmljbz9fX25leHRfbWV0YWRhdGFfX1wiKSkuZGVmYXVsdChwcm9wcykpXSxcbiAgICBhcHBsZTogW10sXG4gICAgb3BlbkdyYXBoOiBbXSxcbiAgICB0d2l0dGVyOiBbXSxcbiAgICBtYW5pZmVzdDogdW5kZWZpbmVkXG4gIH1cbiAgICAgIH1cbiAgICAgIF1cbiAgICAgIH0sXG4gICAgICAgIHtcbiAgICAgICAgJ2xheW91dCc6IFttb2R1bGUwLCBcIkM6XFxcXFVzZXJzXFxcXERBQkJJRVxcXFxPbmVEcml2ZVxcXFxEZXNrdG9wXFxcXGxndS1wcm9qZWN0LWFwcFxcXFxzcmNcXFxcYXBwXFxcXGxheW91dC50c3hcIl0sXG4nbm90LWZvdW5kJzogW21vZHVsZTEsIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL25vdC1mb3VuZC1lcnJvclwiXSxcbidmb3JiaWRkZW4nOiBbbW9kdWxlMiwgXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvZm9yYmlkZGVuLWVycm9yXCJdLFxuJ3VuYXV0aG9yaXplZCc6IFttb2R1bGUzLCBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy91bmF1dGhvcml6ZWQtZXJyb3JcIl0sXG4gICAgICAgIG1ldGFkYXRhOiB7XG4gICAgaWNvbjogWyhhc3luYyAocHJvcHMpID0+IChhd2FpdCBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIm5leHQtbWV0YWRhdGEtaW1hZ2UtbG9hZGVyP3R5cGU9ZmF2aWNvbiZzZWdtZW50PSZiYXNlUGF0aD0mcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyFDOlxcXFxVc2Vyc1xcXFxEQUJCSUVcXFxcT25lRHJpdmVcXFxcRGVza3RvcFxcXFxsZ3UtcHJvamVjdC1hcHBcXFxcc3JjXFxcXGFwcFxcXFxmYXZpY29uLmljbz9fX25leHRfbWV0YWRhdGFfX1wiKSkuZGVmYXVsdChwcm9wcykpXSxcbiAgICBhcHBsZTogW10sXG4gICAgb3BlbkdyYXBoOiBbXSxcbiAgICB0d2l0dGVyOiBbXSxcbiAgICBtYW5pZmVzdDogdW5kZWZpbmVkXG4gIH1cbiAgICAgIH1cbiAgICAgIF1cbiAgICAgIH0uY2hpbGRyZW47XG5jb25zdCBwYWdlcyA9IFtcIkM6XFxcXFVzZXJzXFxcXERBQkJJRVxcXFxPbmVEcml2ZVxcXFxEZXNrdG9wXFxcXGxndS1wcm9qZWN0LWFwcFxcXFxzcmNcXFxcYXBwXFxcXGF1dGhcXFxcbG9naW5cXFxccGFnZS50c3hcIl07XG5leHBvcnQgeyB0cmVlLCBwYWdlcyB9O1xuZXhwb3J0IHsgZGVmYXVsdCBhcyBHbG9iYWxFcnJvciB9IGZyb20gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvZXJyb3ItYm91bmRhcnlcIiB3aXRoIHtcbiAgICAndHVyYm9wYWNrLXRyYW5zaXRpb24nOiAnbmV4dC1zZXJ2ZXItdXRpbGl0eSdcbn07XG5jb25zdCBfX25leHRfYXBwX3JlcXVpcmVfXyA9IF9fd2VicGFja19yZXF1aXJlX19cbmNvbnN0IF9fbmV4dF9hcHBfbG9hZF9jaHVua19fID0gKCkgPT4gUHJvbWlzZS5yZXNvbHZlKClcbmV4cG9ydCBjb25zdCBfX25leHRfYXBwX18gPSB7XG4gICAgcmVxdWlyZTogX19uZXh0X2FwcF9yZXF1aXJlX18sXG4gICAgbG9hZENodW5rOiBfX25leHRfYXBwX2xvYWRfY2h1bmtfX1xufTtcbmV4cG9ydCAqIGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2FwcC1yZW5kZXIvZW50cnktYmFzZVwiIHdpdGgge1xuICAgICd0dXJib3BhY2stdHJhbnNpdGlvbic6ICduZXh0LXNlcnZlci11dGlsaXR5J1xufTtcbi8vIENyZWF0ZSBhbmQgZXhwb3J0IHRoZSByb3V0ZSBtb2R1bGUgdGhhdCB3aWxsIGJlIGNvbnN1bWVkLlxuZXhwb3J0IGNvbnN0IHJvdXRlTW9kdWxlID0gbmV3IEFwcFBhZ2VSb3V0ZU1vZHVsZSh7XG4gICAgZGVmaW5pdGlvbjoge1xuICAgICAgICBraW5kOiBSb3V0ZUtpbmQuQVBQX1BBR0UsXG4gICAgICAgIHBhZ2U6IFwiL2F1dGgvbG9naW4vcGFnZVwiLFxuICAgICAgICBwYXRobmFtZTogXCIvYXV0aC9sb2dpblwiLFxuICAgICAgICAvLyBUaGUgZm9sbG93aW5nIGFyZW4ndCB1c2VkIGluIHByb2R1Y3Rpb24uXG4gICAgICAgIGJ1bmRsZVBhdGg6ICcnLFxuICAgICAgICBmaWxlbmFtZTogJycsXG4gICAgICAgIGFwcFBhdGhzOiBbXVxuICAgIH0sXG4gICAgdXNlcmxhbmQ6IHtcbiAgICAgICAgbG9hZGVyVHJlZTogdHJlZVxuICAgIH1cbn0pO1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1hcHAtcGFnZS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fauth%2Flogin%2Fpage&page=%2Fauth%2Flogin%2Fpage&appPaths=%2Fauth%2Flogin%2Fpage&pagePath=private-next-app-dir%2Fauth%2Flogin%2Fpage.tsx&appDir=C%3A%5CUsers%5CDABBIE%5COneDrive%5CDesktop%5Clgu-project-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDABBIE%5COneDrive%5CDesktop%5Clgu-project-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Csrc%5C%5Ccomponents%5C%5Cchatbot%5C%5CChatBot.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CReduxProvider.tsx%22%2C%22ids%22%3A%5B%22ReduxProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CSupabaseAuthProvider.tsx%22%2C%22ids%22%3A%5B%22SupabaseAuthProvider%22%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Csrc%5C%5Ccomponents%5C%5Cchatbot%5C%5CChatBot.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CReduxProvider.tsx%22%2C%22ids%22%3A%5B%22ReduxProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CSupabaseAuthProvider.tsx%22%2C%22ids%22%3A%5B%22SupabaseAuthProvider%22%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/chatbot/ChatBot.tsx */ \"(rsc)/./src/components/chatbot/ChatBot.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/ReduxProvider.tsx */ \"(rsc)/./src/components/providers/ReduxProvider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/SupabaseAuthProvider.tsx */ \"(rsc)/./src/components/providers/SupabaseAuthProvider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Csrc%5C%5Ccomponents%5C%5Cchatbot%5C%5CChatBot.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CReduxProvider.tsx%22%2C%22ids%22%3A%5B%22ReduxProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CSupabaseAuthProvider.tsx%22%2C%22ids%22%3A%5B%22SupabaseAuthProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/auth/login/page.tsx */ \"(rsc)/./src/app/auth/login/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0RBQkJJRSU1QyU1Q09uZURyaXZlJTVDJTVDRGVza3RvcCU1QyU1Q2xndS1wcm9qZWN0LWFwcCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2F1dGglNUMlNUNsb2dpbiU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxzS0FBMkgiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXERBQkJJRVxcXFxPbmVEcml2ZVxcXFxEZXNrdG9wXFxcXGxndS1wcm9qZWN0LWFwcFxcXFxzcmNcXFxcYXBwXFxcXGF1dGhcXFxcbG9naW5cXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcREFCQklFXFxPbmVEcml2ZVxcRGVza3RvcFxcbGd1LXByb2plY3QtYXBwXFxzcmNcXGFwcFxcZmF2aWNvbi5pY28/X19uZXh0X21ldGFkYXRhX18iXSwic291cmNlc0NvbnRlbnQiOlsiICBpbXBvcnQgeyBmaWxsTWV0YWRhdGFTZWdtZW50IH0gZnJvbSAnbmV4dC9kaXN0L2xpYi9tZXRhZGF0YS9nZXQtbWV0YWRhdGEtcm91dGUnXG5cbiAgZXhwb3J0IGRlZmF1bHQgYXN5bmMgKHByb3BzKSA9PiB7XG4gICAgY29uc3QgaW1hZ2VEYXRhID0ge1widHlwZVwiOlwiaW1hZ2UveC1pY29uXCIsXCJzaXplc1wiOlwiMTZ4MTZcIn1cbiAgICBjb25zdCBpbWFnZVVybCA9IGZpbGxNZXRhZGF0YVNlZ21lbnQoXCIuXCIsIGF3YWl0IHByb3BzLnBhcmFtcywgXCJmYXZpY29uLmljb1wiKVxuXG4gICAgcmV0dXJuIFt7XG4gICAgICAuLi5pbWFnZURhdGEsXG4gICAgICB1cmw6IGltYWdlVXJsICsgXCJcIixcbiAgICB9XVxuICB9Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/auth/login/page.tsx":
/*!*************************************!*\
  !*** ./src/app/auth/login/page.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js\");\n/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(\nfunction() { throw new Error(\"Attempted to call the default export of \\\"C:\\\\\\\\Users\\\\\\\\<USER>\\\\\\\\OneDrive\\\\\\\\Desktop\\\\\\\\lgu-project-app\\\\\\\\src\\\\\\\\app\\\\\\\\auth\\\\\\\\login\\\\\\\\page.tsx\\\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n\"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n\"default\",\n));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./src/app/auth/login/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"be58c1ae52fb\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXERBQkJJRVxcT25lRHJpdmVcXERlc2t0b3BcXGxndS1wcm9qZWN0LWFwcFxcc3JjXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiYmU1OGMxYWU1MmZiXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_providers_ReduxProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/providers/ReduxProvider */ \"(rsc)/./src/components/providers/ReduxProvider.tsx\");\n/* harmony import */ var _components_providers_SupabaseAuthProvider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/providers/SupabaseAuthProvider */ \"(rsc)/./src/components/providers/SupabaseAuthProvider.tsx\");\n/* harmony import */ var _components_chatbot_ChatBot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/chatbot/ChatBot */ \"(rsc)/./src/components/chatbot/ChatBot.tsx\");\n\n\n\n\n\n\n\nconst metadata = {\n    title: \"LGU Project App - Admin Dashboard\",\n    description: \"Admin dashboard for managing users and system operations\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_5___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_6___default().variable)} antialiased`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers_ReduxProvider__WEBPACK_IMPORTED_MODULE_2__.ReduxProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers_SupabaseAuthProvider__WEBPACK_IMPORTED_MODULE_3__.SupabaseAuthProvider, {\n                    children: [\n                        children,\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chatbot_ChatBot__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 36,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 33,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 30,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/chatbot/ChatBot.tsx":
/*!********************************************!*\
  !*** ./src/components/chatbot/ChatBot.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\components\\\\chatbot\\\\ChatBot.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\components\\chatbot\\ChatBot.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/providers/ReduxProvider.tsx":
/*!****************************************************!*\
  !*** ./src/components/providers/ReduxProvider.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ReduxProvider: () => (/* binding */ ReduxProvider),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js\");\n/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);\n\nconst ReduxProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(\nfunction() { throw new Error(\"Attempted to call ReduxProvider() from the server but ReduxProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n\"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\components\\\\providers\\\\ReduxProvider.tsx\",\n\"ReduxProvider\",\n);/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(\nfunction() { throw new Error(\"Attempted to call the default export of \\\"C:\\\\\\\\Users\\\\\\\\<USER>\\\\\\\\OneDrive\\\\\\\\Desktop\\\\\\\\lgu-project-app\\\\\\\\src\\\\\\\\components\\\\\\\\providers\\\\\\\\ReduxProvider.tsx\\\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n\"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\components\\\\providers\\\\ReduxProvider.tsx\",\n\"default\",\n));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY29tcG9uZW50cy9wcm92aWRlcnMvUmVkdXhQcm92aWRlci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFBOzs7Ozs7OztDQVFDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXERBQkJJRVxcT25lRHJpdmVcXERlc2t0b3BcXGxndS1wcm9qZWN0LWFwcFxcc3JjXFxjb21wb25lbnRzXFxwcm92aWRlcnNcXFJlZHV4UHJvdmlkZXIudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogUmVkdXggUHJvdmlkZXIgQ29tcG9uZW50XG4gKiBcbiAqIFRoaXMgY29tcG9uZW50IHdyYXBzIHRoZSBhcHBsaWNhdGlvbiB3aXRoIFJlZHV4IFByb3ZpZGVyIGFuZCBoYW5kbGVzOlxuICogLSBSZWR1eCBzdG9yZSBpbml0aWFsaXphdGlvblxuICogLSBJbnRlZ3JhdGlvbiB3aXRoIFN1cGFiYXNlIEF1dGhcbiAqIC0gU2V0dGluZ3MgaW5pdGlhbGl6YXRpb25cbiAqIC0gRXJyb3IgYm91bmRhcnkgZm9yIFJlZHV4IG9wZXJhdGlvbnNcbiAqL1xuXG4ndXNlIGNsaWVudCdcblxuaW1wb3J0IHsgdXNlRWZmZWN0LCB1c2VSZWYgfSBmcm9tICdyZWFjdCdcbmltcG9ydCB7IFByb3ZpZGVyIH0gZnJvbSAncmVhY3QtcmVkdXgnXG5pbXBvcnQgeyBzdG9yZSwgdXNlQXBwRGlzcGF0Y2ggfSBmcm9tICdAL2xpYi9zdG9yZSdcbmltcG9ydCB7IGluaXRpYWxpemVBdXRoLCBzZXRBdXRoU3RhdGUgfSBmcm9tICdAL2xpYi9yZWR1eC9zbGljZXMvYXV0aFNsaWNlJ1xuaW1wb3J0IHsgbG9hZFNldHRpbmdzIH0gZnJvbSAnQC9saWIvcmVkdXgvc2xpY2VzL3NldHRpbmdzU2xpY2UnXG5pbXBvcnQgeyBjcmVhdGVDbGllbnQgfSBmcm9tICdAL3V0aWxzL3N1cGFiYXNlL2NsaWVudCdcblxuaW50ZXJmYWNlIFJlZHV4UHJvdmlkZXJQcm9wcyB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGVcbn1cblxuLy8gSW5uZXIgY29tcG9uZW50IHRvIGhhbmRsZSBpbml0aWFsaXphdGlvblxuZnVuY3Rpb24gUmVkdXhJbml0aWFsaXplcih7IGNoaWxkcmVuIH06IHsgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZSB9KSB7XG4gIGNvbnN0IGRpc3BhdGNoID0gdXNlQXBwRGlzcGF0Y2goKVxuICBjb25zdCBpbml0aWFsaXplZCA9IHVzZVJlZihmYWxzZSlcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmIChpbml0aWFsaXplZC5jdXJyZW50KSByZXR1cm5cbiAgICBpbml0aWFsaXplZC5jdXJyZW50ID0gdHJ1ZVxuXG4gICAgLy8gSW5pdGlhbGl6ZSBzZXR0aW5nc1xuICAgIGRpc3BhdGNoKGxvYWRTZXR0aW5ncygpKVxuXG4gICAgLy8gSW5pdGlhbGl6ZSBhdXRoXG4gICAgZGlzcGF0Y2goaW5pdGlhbGl6ZUF1dGgoKSlcblxuICAgIC8vIFNldCB1cCBTdXBhYmFzZSBhdXRoIGxpc3RlbmVyXG4gICAgY29uc3Qgc3VwYWJhc2UgPSBjcmVhdGVDbGllbnQoKVxuICAgIFxuICAgIGNvbnN0IHsgZGF0YTogeyBzdWJzY3JpcHRpb24gfSB9ID0gc3VwYWJhc2UuYXV0aC5vbkF1dGhTdGF0ZUNoYW5nZShcbiAgICAgIGFzeW5jIChldmVudCwgc2Vzc2lvbikgPT4ge1xuICAgICAgICB0cnkge1xuICAgICAgICAgIGRpc3BhdGNoKHNldEF1dGhTdGF0ZSh7XG4gICAgICAgICAgICB1c2VyOiBzZXNzaW9uPy51c2VyIHx8IG51bGwsXG4gICAgICAgICAgICBzZXNzaW9uOiBzZXNzaW9uLFxuICAgICAgICAgIH0pKVxuXG4gICAgICAgICAgLy8gSGFuZGxlIHNwZWNpZmljIGF1dGggZXZlbnRzXG4gICAgICAgICAgc3dpdGNoIChldmVudCkge1xuICAgICAgICAgICAgY2FzZSAnU0lHTkVEX0lOJzpcbiAgICAgICAgICAgICAgY29uc29sZS5sb2coJ1VzZXIgc2lnbmVkIGluOicsIHNlc3Npb24/LnVzZXI/LmVtYWlsKVxuICAgICAgICAgICAgICBicmVha1xuICAgICAgICAgICAgY2FzZSAnU0lHTkVEX09VVCc6XG4gICAgICAgICAgICAgIGNvbnNvbGUubG9nKCdVc2VyIHNpZ25lZCBvdXQnKVxuICAgICAgICAgICAgICBicmVha1xuICAgICAgICAgICAgY2FzZSAnVE9LRU5fUkVGUkVTSEVEJzpcbiAgICAgICAgICAgICAgY29uc29sZS5sb2coJ1Rva2VuIHJlZnJlc2hlZCcpXG4gICAgICAgICAgICAgIGJyZWFrXG4gICAgICAgICAgICBjYXNlICdVU0VSX1VQREFURUQnOlxuICAgICAgICAgICAgICBjb25zb2xlLmxvZygnVXNlciB1cGRhdGVkJylcbiAgICAgICAgICAgICAgYnJlYWtcbiAgICAgICAgICAgIGRlZmF1bHQ6XG4gICAgICAgICAgICAgIGJyZWFrXG4gICAgICAgICAgfVxuICAgICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGhhbmRsaW5nIGF1dGggc3RhdGUgY2hhbmdlOicsIGVycm9yKVxuICAgICAgICB9XG4gICAgICB9XG4gICAgKVxuXG4gICAgcmV0dXJuICgpID0+IHtcbiAgICAgIHN1YnNjcmlwdGlvbi51bnN1YnNjcmliZSgpXG4gICAgfVxuICB9LCBbZGlzcGF0Y2hdKVxuXG4gIHJldHVybiA8PntjaGlsZHJlbn08Lz5cbn1cblxuZXhwb3J0IGZ1bmN0aW9uIFJlZHV4UHJvdmlkZXIoeyBjaGlsZHJlbiB9OiBSZWR1eFByb3ZpZGVyUHJvcHMpIHtcbiAgcmV0dXJuIChcbiAgICA8UHJvdmlkZXIgc3RvcmU9e3N0b3JlfT5cbiAgICAgIDxSZWR1eEluaXRpYWxpemVyPlxuICAgICAgICB7Y2hpbGRyZW59XG4gICAgICA8L1JlZHV4SW5pdGlhbGl6ZXI+XG4gICAgPC9Qcm92aWRlcj5cbiAgKVxufVxuXG5leHBvcnQgZGVmYXVsdCBSZWR1eFByb3ZpZGVyXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/components/providers/ReduxProvider.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/providers/SupabaseAuthProvider.tsx":
/*!***********************************************************!*\
  !*** ./src/components/providers/SupabaseAuthProvider.tsx ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SupabaseAuthProvider: () => (/* binding */ SupabaseAuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js\");\n/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);\n\nconst useAuth = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(\nfunction() { throw new Error(\"Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n\"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\components\\\\providers\\\\SupabaseAuthProvider.tsx\",\n\"useAuth\",\n);const SupabaseAuthProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(\nfunction() { throw new Error(\"Attempted to call SupabaseAuthProvider() from the server but SupabaseAuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n\"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\components\\\\providers\\\\SupabaseAuthProvider.tsx\",\n\"SupabaseAuthProvider\",\n);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/providers/SupabaseAuthProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(ssr)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Csrc%5C%5Ccomponents%5C%5Cchatbot%5C%5CChatBot.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CReduxProvider.tsx%22%2C%22ids%22%3A%5B%22ReduxProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CSupabaseAuthProvider.tsx%22%2C%22ids%22%3A%5B%22SupabaseAuthProvider%22%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Csrc%5C%5Ccomponents%5C%5Cchatbot%5C%5CChatBot.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CReduxProvider.tsx%22%2C%22ids%22%3A%5B%22ReduxProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CSupabaseAuthProvider.tsx%22%2C%22ids%22%3A%5B%22SupabaseAuthProvider%22%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/chatbot/ChatBot.tsx */ \"(ssr)/./src/components/chatbot/ChatBot.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/ReduxProvider.tsx */ \"(ssr)/./src/components/providers/ReduxProvider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/SupabaseAuthProvider.tsx */ \"(ssr)/./src/components/providers/SupabaseAuthProvider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Csrc%5C%5Ccomponents%5C%5Cchatbot%5C%5CChatBot.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CReduxProvider.tsx%22%2C%22ids%22%3A%5B%22ReduxProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CSupabaseAuthProvider.tsx%22%2C%22ids%22%3A%5B%22SupabaseAuthProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/auth/login/page.tsx */ \"(ssr)/./src/app/auth/login/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0RBQkJJRSU1QyU1Q09uZURyaXZlJTVDJTVDRGVza3RvcCU1QyU1Q2xndS1wcm9qZWN0LWFwcCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2F1dGglNUMlNUNsb2dpbiU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxzS0FBMkgiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXERBQkJJRVxcXFxPbmVEcml2ZVxcXFxEZXNrdG9wXFxcXGxndS1wcm9qZWN0LWFwcFxcXFxzcmNcXFxcYXBwXFxcXGF1dGhcXFxcbG9naW5cXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDABBIE%5C%5COneDrive%5C%5CDesktop%5C%5Clgu-project-app%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/auth/login/page.tsx":
/*!*************************************!*\
  !*** ./src/app/auth/login/page.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoginPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-hook-form */ \"(ssr)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(ssr)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! zod */ \"(ssr)/./node_modules/zod/dist/esm/index.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _utils_supabase_client__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/supabase/client */ \"(ssr)/./src/utils/supabase/client.ts\");\n/**\n * Supabase Authentication Login Page\n * \n * This page provides both email/password login and demo authentication\n * following the user's preference for immediate access with demo credentials.\n */ /* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nconst loginSchema = zod__WEBPACK_IMPORTED_MODULE_4__.z.object({\n    email: zod__WEBPACK_IMPORTED_MODULE_4__.z.string().email('Invalid email address'),\n    password: zod__WEBPACK_IMPORTED_MODULE_4__.z.string().min(1, 'Password is required')\n});\nfunction LoginPage() {\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const supabase = (0,_utils_supabase_client__WEBPACK_IMPORTED_MODULE_6__.createClient)();\n    // Fix hydration mismatch by ensuring client-side rendering\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LoginPage.useEffect\": ()=>{\n            setIsClient(true);\n        }\n    }[\"LoginPage.useEffect\"], []);\n    const { register, handleSubmit, formState: { errors } } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_7__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_3__.zodResolver)(loginSchema),\n        defaultValues: {\n            email: '<EMAIL>',\n            password: 'demo123'\n        }\n    });\n    const onSubmit = async (data)=>{\n        setIsLoading(true);\n        setError('');\n        try {\n            // Always use the same authentication flow for both demo and regular users\n            console.log('Attempting to sign in with:', data.email);\n            const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({\n                email: data.email,\n                password: data.password\n            });\n            if (signInError) {\n                console.error('Sign-in failed:', signInError.message);\n                console.error('Error details:', signInError);\n                // Show specific error message\n                if (signInError.message.includes('Invalid login credentials')) {\n                    setError('Invalid email or password. Please check your credentials.');\n                } else if (signInError.message.includes('Email not confirmed')) {\n                    setError('Please confirm your email address before signing in.');\n                } else if (signInError.message.includes('Too many requests')) {\n                    setError('Too many login attempts. Please wait a moment and try again.');\n                } else {\n                    setError(`Authentication failed: ${signInError.message}`);\n                }\n                return;\n            }\n            console.log('Sign-in successful:', signInData.user?.email);\n            // Check if we have a valid session\n            if (!signInData.session) {\n                setError('Authentication successful but no session created. Please try again.');\n                return;\n            }\n            // Get redirect URL from query params or default to admin\n            const urlParams = new URLSearchParams(window.location.search);\n            const redirectTo = urlParams.get('redirectTo') || '/admin';\n            router.push(redirectTo);\n            router.refresh();\n        } catch  {\n            setError('An error occurred. Please try again.');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Show loading state during hydration to prevent mismatch\n    if (!isClient) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-md w-full space-y-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-pulse\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-8 bg-gray-200 rounded w-3/4 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 107,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-4 bg-gray-200 rounded w-1/2 mx-auto mb-8\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-10 bg-gray-200 rounded\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-10 bg-gray-200 rounded\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-10 bg-gray-200 rounded\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                    lineNumber: 106,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                lineNumber: 105,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n            lineNumber: 104,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"mt-6 text-center text-3xl font-extrabold text-gray-900\",\n                            children: \"Sign in to your account\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 124,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-center text-sm text-gray-600\",\n                            children: [\n                                \"Or\",\n                                ' ',\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                    href: \"/auth/register\",\n                                    className: \"font-medium text-blue-600 hover:text-blue-500\",\n                                    children: \"create a new account\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 127,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                    lineNumber: 123,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    className: \"mt-8 space-y-6\",\n                    onSubmit: handleSubmit(onSubmit),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"rounded-md shadow-sm -space-y-px\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"email\",\n                                            className: \"sr-only\",\n                                            children: \"Email address\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            ...register('email'),\n                                            type: \"email\",\n                                            autoComplete: \"email\",\n                                            className: \"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-600 text-gray-900 bg-white rounded-t-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm\",\n                                            placeholder: \"Email address\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 15\n                                        }, this),\n                                        errors.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-sm text-red-600\",\n                                            children: errors.email.message\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 152,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"password\",\n                                            className: \"sr-only\",\n                                            children: \"Password\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            ...register('password'),\n                                            type: \"password\",\n                                            autoComplete: \"current-password\",\n                                            className: \"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-600 text-gray-900 bg-white rounded-b-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm\",\n                                            placeholder: \"Password\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 15\n                                        }, this),\n                                        errors.password && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-sm text-red-600\",\n                                            children: errors.password.message\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 11\n                        }, this),\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"rounded-md bg-red-50 p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-red-800\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                lineNumber: 174,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 173,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-50 border border-blue-200 rounded-md p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-blue-800\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Demo Access:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" Use <EMAIL> / demo123 for immediate access\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 178,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                disabled: isLoading,\n                                className: \"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                children: isLoading ? 'Signing in...' : 'Sign in'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 184,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                    lineNumber: 138,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n            lineNumber: 122,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n        lineNumber: 121,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/./src/app/auth/login/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/chatbot/ChatBot.tsx":
/*!********************************************!*\
  !*** ./src/components/chatbot/ChatBot.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ChatBot)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_MessageCircle_Minimize2_Send_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=MessageCircle,Minimize2,Send,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/minimize-2.js\");\n/* harmony import */ var _barrel_optimize_names_MessageCircle_Minimize2_Send_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=MessageCircle,Minimize2,Send,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_MessageCircle_Minimize2_Send_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=MessageCircle,Minimize2,Send,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_MessageCircle_Minimize2_Send_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=MessageCircle,Minimize2,Send,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction ChatBot() {\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMinimized, setIsMinimized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [inputMessage, setInputMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isInitialized, setIsInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Initialize with welcome message\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatBot.useEffect\": ()=>{\n            if (isOpen && !isInitialized) {\n                const welcomeMessage = {\n                    id: Date.now().toString(),\n                    role: 'assistant',\n                    content: 'Hello! I\\'m your AI assistant. How can I help you today?',\n                    timestamp: new Date()\n                };\n                setMessages([\n                    welcomeMessage\n                ]);\n                setIsInitialized(true);\n            }\n        }\n    }[\"ChatBot.useEffect\"], [\n        isOpen,\n        isInitialized\n    ]);\n    // Handle opening the chat\n    const handleOpen = ()=>{\n        if (isMinimized) {\n            setIsMinimized(false);\n            setIsOpen(true);\n        } else {\n            setIsOpen(true);\n        }\n    };\n    // Handle closing the chat\n    const handleClose = ()=>{\n        setIsOpen(false);\n        setIsMinimized(false);\n    };\n    // Handle minimizing the chat\n    const handleMinimize = ()=>{\n        setIsOpen(false);\n        setIsMinimized(true);\n    };\n    // Send message function\n    const sendMessage = async ()=>{\n        if (!inputMessage.trim() || isLoading) return;\n        const userMessage = {\n            id: `user-${Math.random().toString(36).substr(2, 9)}`,\n            role: 'user',\n            content: inputMessage.trim(),\n            timestamp: new Date()\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        setInputMessage('');\n        setIsLoading(true);\n        try {\n            const response = await fetch('/api/chat', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    message: userMessage.content,\n                    conversationHistory: messages\n                })\n            });\n            const data = await response.json();\n            if (data.success) {\n                const assistantMessage = {\n                    id: `assistant-${Math.random().toString(36).substr(2, 9)}`,\n                    role: 'assistant',\n                    content: data.message,\n                    timestamp: new Date()\n                };\n                setMessages((prev)=>[\n                        ...prev,\n                        assistantMessage\n                    ]);\n            } else {\n                throw new Error(data.error || 'Failed to get response');\n            }\n        } catch (error) {\n            console.error('Chat error:', error);\n            const errorMessage = {\n                id: (Date.now() + 1).toString(),\n                role: 'assistant',\n                content: 'I apologize, but I\\'m experiencing technical difficulties. Please try again in a moment.',\n                timestamp: new Date()\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    errorMessage\n                ]);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Handle key press\n    const handleKeyPress = (e)=>{\n        if (e.key === 'Enter' && !e.shiftKey) {\n            e.preventDefault();\n            sendMessage();\n        }\n    };\n    // Handle clicks outside to close\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatBot.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"ChatBot.useEffect.handleClickOutside\": (event)=>{\n                    const target = event.target;\n                    if (isOpen && !target.closest('.chat-container') && !target.closest('.chat-bubble')) {\n                        handleClose();\n                    }\n                }\n            }[\"ChatBot.useEffect.handleClickOutside\"];\n            if (isOpen) {\n                document.addEventListener('mousedown', handleClickOutside);\n                return ({\n                    \"ChatBot.useEffect\": ()=>document.removeEventListener('mousedown', handleClickOutside)\n                })[\"ChatBot.useEffect\"];\n            }\n        }\n    }[\"ChatBot.useEffect\"], [\n        isOpen\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: handleOpen,\n                className: `chat-bubble fixed left-4 bottom-4 ${isMinimized ? 'w-12 h-12' : 'w-14 h-14'} bg-blue-500 hover:bg-blue-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center z-50 max-sm:left-2 max-sm:bottom-2 max-sm:w-12 max-sm:h-12`,\n                title: isMinimized ? 'Restore Chat' : 'Open Chat Assistant',\n                children: [\n                    isMinimized ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircle_Minimize2_Send_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        size: 20,\n                        className: \"transform rotate-180\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\components\\\\chatbot\\\\ChatBot.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircle_Minimize2_Send_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        size: 24\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\components\\\\chatbot\\\\ChatBot.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 11\n                    }, this),\n                    !isMinimized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 rounded-full bg-blue-400 animate-ping opacity-20\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\components\\\\chatbot\\\\ChatBot.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\components\\\\chatbot\\\\ChatBot.tsx\",\n                lineNumber: 141,\n                columnNumber: 7\n            }, this),\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"chat-container fixed left-4 bottom-20 w-80 sm:w-96 h-[500px] bg-white rounded-lg shadow-2xl border border-gray-200 flex flex-col z-50 max-h-[80vh] max-sm:left-2 max-sm:right-2 max-sm:w-auto max-sm:h-[70vh] max-sm:bottom-16\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between p-4 border-b border-gray-200 bg-blue-500 text-white rounded-t-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium\",\n                                            children: \"AI\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\components\\\\chatbot\\\\ChatBot.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\components\\\\chatbot\\\\ChatBot.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-medium\",\n                                                children: \"AI Assistant\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\components\\\\chatbot\\\\ChatBot.tsx\",\n                                                lineNumber: 168,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-blue-100\",\n                                                children: \"Online\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\components\\\\chatbot\\\\ChatBot.tsx\",\n                                                lineNumber: 169,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\components\\\\chatbot\\\\ChatBot.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\components\\\\chatbot\\\\ChatBot.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleMinimize,\n                                        className: \"p-1 hover:bg-white hover:bg-opacity-20 rounded transition-colors\",\n                                        title: \"Minimize\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircle_Minimize2_Send_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\components\\\\chatbot\\\\ChatBot.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\components\\\\chatbot\\\\ChatBot.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleClose,\n                                        className: \"p-1 hover:bg-white hover:bg-opacity-20 rounded transition-colors\",\n                                        title: \"Close\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircle_Minimize2_Send_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\components\\\\chatbot\\\\ChatBot.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\components\\\\chatbot\\\\ChatBot.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\components\\\\chatbot\\\\ChatBot.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\components\\\\chatbot\\\\ChatBot.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 overflow-y-auto p-4 space-y-4\",\n                        children: [\n                            messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `flex ${message.role === 'user' ? 'justify-end' : 'justify-start'} mb-4`,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `flex max-w-[80%] ${message.role === 'user' ? 'flex-row-reverse' : 'flex-row'} items-start gap-2`,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${message.role === 'user' ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700'}`,\n                                                children: message.role === 'user' ? 'U' : 'AI'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\components\\\\chatbot\\\\ChatBot.tsx\",\n                                                lineNumber: 198,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `rounded-lg px-4 py-2 ${message.role === 'user' ? 'bg-blue-500 text-white rounded-br-sm' : 'bg-gray-100 text-gray-800 rounded-bl-sm'}`,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm leading-relaxed whitespace-pre-wrap break-words\",\n                                                        children: message.content\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\components\\\\chatbot\\\\ChatBot.tsx\",\n                                                        lineNumber: 208,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: `text-xs mt-1 ${message.role === 'user' ? 'text-blue-100' : 'text-gray-500'}`,\n                                                        children: message.timestamp.toLocaleTimeString([], {\n                                                            hour: '2-digit',\n                                                            minute: '2-digit'\n                                                        })\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\components\\\\chatbot\\\\ChatBot.tsx\",\n                                                        lineNumber: 211,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\components\\\\chatbot\\\\ChatBot.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\components\\\\chatbot\\\\ChatBot.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 17\n                                    }, this)\n                                }, message.id, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\components\\\\chatbot\\\\ChatBot.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 15\n                                }, this)),\n                            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-start mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-gray-700\",\n                                                children: \"AI\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\components\\\\chatbot\\\\ChatBot.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\components\\\\chatbot\\\\ChatBot.tsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gray-100 rounded-lg px-4 py-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\components\\\\chatbot\\\\ChatBot.tsx\",\n                                                        lineNumber: 232,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\",\n                                                        style: {\n                                                            animationDelay: '0.1s'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\components\\\\chatbot\\\\ChatBot.tsx\",\n                                                        lineNumber: 233,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\",\n                                                        style: {\n                                                            animationDelay: '0.2s'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\components\\\\chatbot\\\\ChatBot.tsx\",\n                                                        lineNumber: 234,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\components\\\\chatbot\\\\ChatBot.tsx\",\n                                                lineNumber: 231,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\components\\\\chatbot\\\\ChatBot.tsx\",\n                                            lineNumber: 230,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\components\\\\chatbot\\\\ChatBot.tsx\",\n                                    lineNumber: 226,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\components\\\\chatbot\\\\ChatBot.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\components\\\\chatbot\\\\ChatBot.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 border-t border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        value: inputMessage,\n                                        onChange: (e)=>setInputMessage(e.target.value),\n                                        onKeyPress: handleKeyPress,\n                                        placeholder: \"Type your message...\",\n                                        className: \"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm bg-white text-gray-900 placeholder-gray-600\",\n                                        disabled: isLoading,\n                                        autoFocus: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\components\\\\chatbot\\\\ChatBot.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: sendMessage,\n                                        disabled: !inputMessage.trim() || isLoading,\n                                        className: \"p-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircle_Minimize2_Send_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\components\\\\chatbot\\\\ChatBot.tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\components\\\\chatbot\\\\ChatBot.tsx\",\n                                        lineNumber: 255,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\components\\\\chatbot\\\\ChatBot.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-gray-500 mt-2 text-center\",\n                                children: \"Press Enter to send • Shift+Enter for new line\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\components\\\\chatbot\\\\ChatBot.tsx\",\n                                lineNumber: 263,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\components\\\\chatbot\\\\ChatBot.tsx\",\n                        lineNumber: 243,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\components\\\\chatbot\\\\ChatBot.tsx\",\n                lineNumber: 160,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/chatbot/ChatBot.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/providers/ReduxProvider.tsx":
/*!****************************************************!*\
  !*** ./src/components/providers/ReduxProvider.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ReduxProvider: () => (/* binding */ ReduxProvider),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-redux */ \"(ssr)/./node_modules/react-redux/dist/react-redux.mjs\");\n/* harmony import */ var _lib_store__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/store */ \"(ssr)/./src/lib/store.ts\");\n/* harmony import */ var _lib_redux_slices_authSlice__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/redux/slices/authSlice */ \"(ssr)/./src/lib/redux/slices/authSlice.ts\");\n/* harmony import */ var _lib_redux_slices_settingsSlice__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/redux/slices/settingsSlice */ \"(ssr)/./src/lib/redux/slices/settingsSlice.ts\");\n/* harmony import */ var _utils_supabase_client__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/supabase/client */ \"(ssr)/./src/utils/supabase/client.ts\");\n/**\n * Redux Provider Component\n * \n * This component wraps the application with Redux Provider and handles:\n * - Redux store initialization\n * - Integration with Supabase Auth\n * - Settings initialization\n * - Error boundary for Redux operations\n */ /* __next_internal_client_entry_do_not_use__ ReduxProvider,default auto */ \n\n\n\n\n\n\n// Inner component to handle initialization\nfunction ReduxInitializer({ children }) {\n    const dispatch = (0,_lib_store__WEBPACK_IMPORTED_MODULE_2__.useAppDispatch)();\n    const initialized = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ReduxInitializer.useEffect\": ()=>{\n            if (initialized.current) return;\n            initialized.current = true;\n            // Initialize settings\n            dispatch((0,_lib_redux_slices_settingsSlice__WEBPACK_IMPORTED_MODULE_4__.loadSettings)());\n            // Initialize auth\n            dispatch((0,_lib_redux_slices_authSlice__WEBPACK_IMPORTED_MODULE_3__.initializeAuth)());\n            // Set up Supabase auth listener\n            const supabase = (0,_utils_supabase_client__WEBPACK_IMPORTED_MODULE_5__.createClient)();\n            const { data: { subscription } } = supabase.auth.onAuthStateChange({\n                \"ReduxInitializer.useEffect\": async (event, session)=>{\n                    try {\n                        dispatch((0,_lib_redux_slices_authSlice__WEBPACK_IMPORTED_MODULE_3__.setAuthState)({\n                            user: session?.user || null,\n                            session: session\n                        }));\n                        // Handle specific auth events\n                        switch(event){\n                            case 'SIGNED_IN':\n                                console.log('User signed in:', session?.user?.email);\n                                break;\n                            case 'SIGNED_OUT':\n                                console.log('User signed out');\n                                break;\n                            case 'TOKEN_REFRESHED':\n                                console.log('Token refreshed');\n                                break;\n                            case 'USER_UPDATED':\n                                console.log('User updated');\n                                break;\n                            default:\n                                break;\n                        }\n                    } catch (error) {\n                        console.error('Error handling auth state change:', error);\n                    }\n                }\n            }[\"ReduxInitializer.useEffect\"]);\n            return ({\n                \"ReduxInitializer.useEffect\": ()=>{\n                    subscription.unsubscribe();\n                }\n            })[\"ReduxInitializer.useEffect\"];\n        }\n    }[\"ReduxInitializer.useEffect\"], [\n        dispatch\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\nfunction ReduxProvider({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_redux__WEBPACK_IMPORTED_MODULE_6__.Provider, {\n        store: _lib_store__WEBPACK_IMPORTED_MODULE_2__.store,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ReduxInitializer, {\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\components\\\\providers\\\\ReduxProvider.tsx\",\n            lineNumber: 84,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\components\\\\providers\\\\ReduxProvider.tsx\",\n        lineNumber: 83,\n        columnNumber: 5\n    }, this);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ReduxProvider);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/providers/ReduxProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/providers/SupabaseAuthProvider.tsx":
/*!***********************************************************!*\
  !*** ./src/components/providers/SupabaseAuthProvider.tsx ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SupabaseAuthProvider: () => (/* binding */ SupabaseAuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils_supabase_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/supabase/client */ \"(ssr)/./src/utils/supabase/client.ts\");\n/**\n * Supabase Authentication Provider\n * \n * This provider manages authentication state across the application.\n * It provides user information, loading states, and authentication methods\n * to all child components.\n */ /* __next_internal_client_entry_do_not_use__ useAuth,SupabaseAuthProvider auto */ \n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    user: null,\n    session: null,\n    loading: true,\n    error: null,\n    signOut: async ()=>{}\n});\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (!context) {\n        throw new Error('useAuth must be used within a SupabaseAuthProvider');\n    }\n    return context;\n};\nfunction SupabaseAuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [session, setSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SupabaseAuthProvider.useEffect\": ()=>{\n            // Check if environment variables are available\n            if (false) {}\n            try {\n                const supabase = (0,_utils_supabase_client__WEBPACK_IMPORTED_MODULE_2__.createClient)();\n                // Get initial session\n                const getInitialSession = {\n                    \"SupabaseAuthProvider.useEffect.getInitialSession\": async ()=>{\n                        try {\n                            const { data: { session }, error } = await supabase.auth.getSession();\n                            if (error) {\n                                console.error('Error getting session:', error);\n                                setError(error.message);\n                            } else {\n                                setSession(session);\n                                setUser(session?.user ?? null);\n                            }\n                        } catch (err) {\n                            console.error('Error in getInitialSession:', err);\n                            setError('Failed to initialize authentication');\n                        } finally{\n                            setLoading(false);\n                        }\n                    }\n                }[\"SupabaseAuthProvider.useEffect.getInitialSession\"];\n                getInitialSession();\n                // Listen for auth changes\n                const { data: { subscription } } = supabase.auth.onAuthStateChange({\n                    \"SupabaseAuthProvider.useEffect\": async (event, session)=>{\n                        try {\n                            setSession(session);\n                            setUser(session?.user ?? null);\n                            setLoading(false);\n                            setError(null);\n                            // Handle sign out\n                            if (event === 'SIGNED_OUT') {\n                                setUser(null);\n                                setSession(null);\n                            }\n                        } catch (err) {\n                            console.error('Error in auth state change:', err);\n                            setError('Authentication state error');\n                        }\n                    }\n                }[\"SupabaseAuthProvider.useEffect\"]);\n                return ({\n                    \"SupabaseAuthProvider.useEffect\": ()=>subscription.unsubscribe()\n                })[\"SupabaseAuthProvider.useEffect\"];\n            } catch (err) {\n                console.error('Error initializing Supabase client:', err);\n                setError('Failed to initialize Supabase client');\n                setLoading(false);\n            }\n        }\n    }[\"SupabaseAuthProvider.useEffect\"], []);\n    const signOut = async ()=>{\n        try {\n            const supabase = (0,_utils_supabase_client__WEBPACK_IMPORTED_MODULE_2__.createClient)();\n            await supabase.auth.signOut();\n        } catch (err) {\n            console.error('Error signing out:', err);\n        }\n    };\n    const value = {\n        user,\n        session,\n        loading,\n        error,\n        signOut\n    };\n    // Show error state if there's a critical error, but allow fallback\n    if (error && !loading) {\n        console.warn('SupabaseAuthProvider error, falling back to no-auth mode:', error);\n        // Instead of blocking the entire app, just provide a fallback context\n        const fallbackValue = {\n            user: null,\n            session: null,\n            loading: false,\n            error,\n            signOut: async ()=>{}\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n            value: fallbackValue,\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\components\\\\providers\\\\SupabaseAuthProvider.tsx\",\n            lineNumber: 141,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\components\\\\providers\\\\SupabaseAuthProvider.tsx\",\n        lineNumber: 148,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/providers/SupabaseAuthProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/redux/slices/authSlice.ts":
/*!*******************************************!*\
  !*** ./src/lib/redux/slices/authSlice.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearAuthError: () => (/* binding */ clearAuthError),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   initializeAuth: () => (/* binding */ initializeAuth),\n/* harmony export */   selectAuth: () => (/* binding */ selectAuth),\n/* harmony export */   selectAuthError: () => (/* binding */ selectAuthError),\n/* harmony export */   selectAuthInitialized: () => (/* binding */ selectAuthInitialized),\n/* harmony export */   selectAuthLoading: () => (/* binding */ selectAuthLoading),\n/* harmony export */   selectIsAuthenticated: () => (/* binding */ selectIsAuthenticated),\n/* harmony export */   selectUser: () => (/* binding */ selectUser),\n/* harmony export */   setAuthLoading: () => (/* binding */ setAuthLoading),\n/* harmony export */   setAuthState: () => (/* binding */ setAuthState),\n/* harmony export */   signInWithPassword: () => (/* binding */ signInWithPassword),\n/* harmony export */   signOut: () => (/* binding */ signOut),\n/* harmony export */   signUp: () => (/* binding */ signUp)\n/* harmony export */ });\n/* harmony import */ var _reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @reduxjs/toolkit */ \"(ssr)/./node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs\");\n/* harmony import */ var _utils_supabase_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/utils/supabase/client */ \"(ssr)/./src/utils/supabase/client.ts\");\n/**\n * Authentication Redux Slice\n * \n * Manages authentication state including:\n * - User authentication status\n * - User profile information\n * - Session management\n * - Authentication loading states\n * - Integration with Supabase Auth\n */ \n\n// Initial state\nconst initialState = {\n    user: null,\n    session: null,\n    loading: true,\n    error: null,\n    isAuthenticated: false,\n    initialized: false\n};\n// Async thunks\nconst initializeAuth = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_1__.createAsyncThunk)('auth/initialize', async (_, { rejectWithValue })=>{\n    try {\n        const supabase = (0,_utils_supabase_client__WEBPACK_IMPORTED_MODULE_0__.createClient)();\n        const { data: { session }, error } = await supabase.auth.getSession();\n        if (error) {\n            throw error;\n        }\n        return {\n            session,\n            user: session?.user || null\n        };\n    } catch (error) {\n        const errorMessage = error instanceof Error ? error.message : 'Failed to initialize authentication';\n        return rejectWithValue(errorMessage);\n    }\n});\nconst signInWithPassword = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_1__.createAsyncThunk)('auth/signInWithPassword', async ({ email, password }, { rejectWithValue })=>{\n    try {\n        const supabase = (0,_utils_supabase_client__WEBPACK_IMPORTED_MODULE_0__.createClient)();\n        const { data, error } = await supabase.auth.signInWithPassword({\n            email,\n            password\n        });\n        if (error) {\n            throw error;\n        }\n        return {\n            session: data.session,\n            user: data.user\n        };\n    } catch (error) {\n        const errorMessage = error instanceof Error ? error.message : 'Failed to sign in';\n        return rejectWithValue(errorMessage);\n    }\n});\nconst signOut = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_1__.createAsyncThunk)('auth/signOut', async (_, { rejectWithValue })=>{\n    try {\n        const supabase = (0,_utils_supabase_client__WEBPACK_IMPORTED_MODULE_0__.createClient)();\n        const { error } = await supabase.auth.signOut();\n        if (error) {\n            throw error;\n        }\n        return null;\n    } catch (error) {\n        const errorMessage = error instanceof Error ? error.message : 'Failed to sign out';\n        return rejectWithValue(errorMessage);\n    }\n});\nconst signUp = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_1__.createAsyncThunk)('auth/signUp', async ({ email, password, metadata }, { rejectWithValue })=>{\n    try {\n        const supabase = (0,_utils_supabase_client__WEBPACK_IMPORTED_MODULE_0__.createClient)();\n        const { data, error } = await supabase.auth.signUp({\n            email,\n            password,\n            options: {\n                data: metadata\n            }\n        });\n        if (error) {\n            throw error;\n        }\n        return {\n            session: data.session,\n            user: data.user\n        };\n    } catch (error) {\n        const errorMessage = error instanceof Error ? error.message : 'Failed to sign up';\n        return rejectWithValue(errorMessage);\n    }\n});\n// Auth slice\nconst authSlice = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_1__.createSlice)({\n    name: 'auth',\n    initialState,\n    reducers: {\n        setAuthState: (state, action)=>{\n            state.user = action.payload.user;\n            state.session = action.payload.session;\n            state.isAuthenticated = !!action.payload.user;\n            state.loading = false;\n            state.error = null;\n            state.initialized = true;\n        },\n        clearAuthError: (state)=>{\n            state.error = null;\n        },\n        setAuthLoading: (state, action)=>{\n            state.loading = action.payload;\n        }\n    },\n    extraReducers: (builder)=>{\n        // Initialize auth\n        builder.addCase(initializeAuth.pending, (state)=>{\n            state.loading = true;\n            state.error = null;\n        }).addCase(initializeAuth.fulfilled, (state, action)=>{\n            state.loading = false;\n            state.user = action.payload.user;\n            state.session = action.payload.session;\n            state.isAuthenticated = !!action.payload.user;\n            state.initialized = true;\n            state.error = null;\n        }).addCase(initializeAuth.rejected, (state, action)=>{\n            state.loading = false;\n            state.error = action.payload;\n            state.initialized = true;\n        });\n        // Sign in\n        builder.addCase(signInWithPassword.pending, (state)=>{\n            state.loading = true;\n            state.error = null;\n        }).addCase(signInWithPassword.fulfilled, (state, action)=>{\n            state.loading = false;\n            state.user = action.payload.user;\n            state.session = action.payload.session;\n            state.isAuthenticated = !!action.payload.user;\n            state.error = null;\n        }).addCase(signInWithPassword.rejected, (state, action)=>{\n            state.loading = false;\n            state.error = action.payload;\n        });\n        // Sign out\n        builder.addCase(signOut.pending, (state)=>{\n            state.loading = true;\n            state.error = null;\n        }).addCase(signOut.fulfilled, (state)=>{\n            state.loading = false;\n            state.user = null;\n            state.session = null;\n            state.isAuthenticated = false;\n            state.error = null;\n        }).addCase(signOut.rejected, (state, action)=>{\n            state.loading = false;\n            state.error = action.payload;\n        });\n        // Sign up\n        builder.addCase(signUp.pending, (state)=>{\n            state.loading = true;\n            state.error = null;\n        }).addCase(signUp.fulfilled, (state, action)=>{\n            state.loading = false;\n            state.user = action.payload.user;\n            state.session = action.payload.session;\n            state.isAuthenticated = !!action.payload.user;\n            state.error = null;\n        }).addCase(signUp.rejected, (state, action)=>{\n            state.loading = false;\n            state.error = action.payload;\n        });\n    }\n});\n// Export actions\nconst { setAuthState, clearAuthError, setAuthLoading } = authSlice.actions;\n// Export selectors\nconst selectAuth = (state)=>state.auth;\nconst selectUser = (state)=>state.auth.user;\nconst selectIsAuthenticated = (state)=>state.auth.isAuthenticated;\nconst selectAuthLoading = (state)=>state.auth.loading;\nconst selectAuthError = (state)=>state.auth.error;\nconst selectAuthInitialized = (state)=>state.auth.initialized;\n// Export reducer\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (authSlice.reducer);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/redux/slices/authSlice.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/redux/slices/personnelSlice.ts":
/*!************************************************!*\
  !*** ./src/lib/redux/slices/personnelSlice.ts ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearPersonnelError: () => (/* binding */ clearPersonnelError),\n/* harmony export */   createPersonnel: () => (/* binding */ createPersonnel),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   deletePersonnel: () => (/* binding */ deletePersonnel),\n/* harmony export */   fetchPersonnel: () => (/* binding */ fetchPersonnel),\n/* harmony export */   fetchPersonnelById: () => (/* binding */ fetchPersonnelById),\n/* harmony export */   resetPersonnelState: () => (/* binding */ resetPersonnelState),\n/* harmony export */   selectPersonnelError: () => (/* binding */ selectPersonnelError),\n/* harmony export */   selectPersonnelFilters: () => (/* binding */ selectPersonnelFilters),\n/* harmony export */   selectPersonnelList: () => (/* binding */ selectPersonnelList),\n/* harmony export */   selectPersonnelLoading: () => (/* binding */ selectPersonnelLoading),\n/* harmony export */   selectPersonnelOperationLoading: () => (/* binding */ selectPersonnelOperationLoading),\n/* harmony export */   selectPersonnelPagination: () => (/* binding */ selectPersonnelPagination),\n/* harmony export */   selectPersonnelViewMode: () => (/* binding */ selectPersonnelViewMode),\n/* harmony export */   selectSelectedPersonnel: () => (/* binding */ selectSelectedPersonnel),\n/* harmony export */   setFilters: () => (/* binding */ setFilters),\n/* harmony export */   setPagination: () => (/* binding */ setPagination),\n/* harmony export */   setSelectedPersonnel: () => (/* binding */ setSelectedPersonnel),\n/* harmony export */   setViewMode: () => (/* binding */ setViewMode),\n/* harmony export */   updatePersonnel: () => (/* binding */ updatePersonnel)\n/* harmony export */ });\n/* harmony import */ var _reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @reduxjs/toolkit */ \"(ssr)/./node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs\");\n/**\n * Personnel Redux Slice\n * \n * Manages personnel data state including:\n * - Personnel list with pagination\n * - Search and filtering\n * - CRUD operations\n * - Loading states and error handling\n * - Sorting and view preferences\n */ \n// Initial state\nconst initialState = {\n    personnel: [],\n    selectedPersonnel: null,\n    pagination: {\n        page: 1,\n        limit: 10,\n        total: 0,\n        pages: 0\n    },\n    filters: {\n        search: '',\n        sortBy: 'name_asc'\n    },\n    viewMode: 'list',\n    loading: false,\n    error: null,\n    operationLoading: false\n};\n// Async thunks\nconst fetchPersonnel = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.createAsyncThunk)('personnel/fetchPersonnel', async (params = {}, { rejectWithValue })=>{\n    try {\n        const searchParams = new URLSearchParams({\n            page: (params.page || 1).toString(),\n            limit: (params.limit || 10).toString(),\n            ...params.search && {\n                search: params.search\n            },\n            sort: params.sortBy || 'name_asc'\n        });\n        const response = await fetch(`/api/personnel?${searchParams}`);\n        if (!response.ok) {\n            throw new Error('Failed to fetch personnel');\n        }\n        const data = await response.json();\n        return {\n            personnel: data.personnel,\n            pagination: data.pagination\n        };\n    } catch (error) {\n        const errorMessage = error instanceof Error ? error.message : 'Failed to fetch personnel';\n        return rejectWithValue(errorMessage);\n    }\n});\nconst fetchPersonnelById = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.createAsyncThunk)('personnel/fetchPersonnelById', async (id, { rejectWithValue })=>{\n    try {\n        const response = await fetch(`/api/personnel/${id}`);\n        if (!response.ok) {\n            throw new Error('Failed to fetch personnel');\n        }\n        const data = await response.json();\n        return data.personnel;\n    } catch (error) {\n        const errorMessage = error instanceof Error ? error.message : 'Failed to fetch personnel';\n        return rejectWithValue(errorMessage);\n    }\n});\nconst createPersonnel = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.createAsyncThunk)('personnel/createPersonnel', async (personnelData, { rejectWithValue })=>{\n    try {\n        const response = await fetch('/api/personnel', {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify(personnelData)\n        });\n        if (!response.ok) {\n            throw new Error('Failed to create personnel');\n        }\n        const data = await response.json();\n        return data.personnel;\n    } catch (error) {\n        const errorMessage = error instanceof Error ? error.message : 'Failed to create personnel';\n        return rejectWithValue(errorMessage);\n    }\n});\nconst updatePersonnel = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.createAsyncThunk)('personnel/updatePersonnel', async ({ id, ...personnelData }, { rejectWithValue })=>{\n    try {\n        const response = await fetch(`/api/personnel/${id}`, {\n            method: 'PUT',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify(personnelData)\n        });\n        if (!response.ok) {\n            throw new Error('Failed to update personnel');\n        }\n        const data = await response.json();\n        return data.personnel;\n    } catch (error) {\n        const errorMessage = error instanceof Error ? error.message : 'Failed to update personnel';\n        return rejectWithValue(errorMessage);\n    }\n});\nconst deletePersonnel = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.createAsyncThunk)('personnel/deletePersonnel', async (id, { rejectWithValue })=>{\n    try {\n        const response = await fetch(`/api/personnel/${id}`, {\n            method: 'DELETE'\n        });\n        if (!response.ok) {\n            throw new Error('Failed to delete personnel');\n        }\n        return id;\n    } catch (error) {\n        const errorMessage = error instanceof Error ? error.message : 'Failed to delete personnel';\n        return rejectWithValue(errorMessage);\n    }\n});\n// Personnel slice\nconst personnelSlice = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.createSlice)({\n    name: 'personnel',\n    initialState,\n    reducers: {\n        setSelectedPersonnel: (state, action)=>{\n            state.selectedPersonnel = action.payload;\n        },\n        setFilters: (state, action)=>{\n            state.filters = {\n                ...state.filters,\n                ...action.payload\n            };\n        },\n        setPagination: (state, action)=>{\n            state.pagination = {\n                ...state.pagination,\n                ...action.payload\n            };\n        },\n        setViewMode: (state, action)=>{\n            state.viewMode = action.payload;\n        },\n        clearPersonnelError: (state)=>{\n            state.error = null;\n        },\n        resetPersonnelState: ()=>{\n            return initialState;\n        }\n    },\n    extraReducers: (builder)=>{\n        // Fetch personnel\n        builder.addCase(fetchPersonnel.pending, (state)=>{\n            state.loading = true;\n            state.error = null;\n        }).addCase(fetchPersonnel.fulfilled, (state, action)=>{\n            state.loading = false;\n            state.personnel = action.payload.personnel;\n            state.pagination = action.payload.pagination;\n            state.error = null;\n        }).addCase(fetchPersonnel.rejected, (state, action)=>{\n            state.loading = false;\n            state.error = action.payload;\n        });\n        // Fetch personnel by ID\n        builder.addCase(fetchPersonnelById.pending, (state)=>{\n            state.loading = true;\n            state.error = null;\n        }).addCase(fetchPersonnelById.fulfilled, (state, action)=>{\n            state.loading = false;\n            state.selectedPersonnel = action.payload;\n            state.error = null;\n        }).addCase(fetchPersonnelById.rejected, (state, action)=>{\n            state.loading = false;\n            state.error = action.payload;\n        });\n        // Create personnel\n        builder.addCase(createPersonnel.pending, (state)=>{\n            state.operationLoading = true;\n            state.error = null;\n        }).addCase(createPersonnel.fulfilled, (state, action)=>{\n            state.operationLoading = false;\n            state.personnel.unshift(action.payload);\n            state.pagination.total += 1;\n            state.error = null;\n        }).addCase(createPersonnel.rejected, (state, action)=>{\n            state.operationLoading = false;\n            state.error = action.payload;\n        });\n        // Update personnel\n        builder.addCase(updatePersonnel.pending, (state)=>{\n            state.operationLoading = true;\n            state.error = null;\n        }).addCase(updatePersonnel.fulfilled, (state, action)=>{\n            state.operationLoading = false;\n            const index = state.personnel.findIndex((p)=>p.id === action.payload.id);\n            if (index !== -1) {\n                state.personnel[index] = action.payload;\n            }\n            if (state.selectedPersonnel?.id === action.payload.id) {\n                state.selectedPersonnel = action.payload;\n            }\n            state.error = null;\n        }).addCase(updatePersonnel.rejected, (state, action)=>{\n            state.operationLoading = false;\n            state.error = action.payload;\n        });\n        // Delete personnel\n        builder.addCase(deletePersonnel.pending, (state)=>{\n            state.operationLoading = true;\n            state.error = null;\n        }).addCase(deletePersonnel.fulfilled, (state, action)=>{\n            state.operationLoading = false;\n            state.personnel = state.personnel.filter((p)=>p.id !== action.payload);\n            state.pagination.total -= 1;\n            if (state.selectedPersonnel?.id === action.payload) {\n                state.selectedPersonnel = null;\n            }\n            state.error = null;\n        }).addCase(deletePersonnel.rejected, (state, action)=>{\n            state.operationLoading = false;\n            state.error = action.payload;\n        });\n    }\n});\n// Export actions\nconst { setSelectedPersonnel, setFilters, setPagination, setViewMode, clearPersonnelError, resetPersonnelState } = personnelSlice.actions;\n// Export selectors\nconst selectPersonnelList = (state)=>state.personnel.personnel;\nconst selectSelectedPersonnel = (state)=>state.personnel.selectedPersonnel;\nconst selectPersonnelPagination = (state)=>state.personnel.pagination;\nconst selectPersonnelFilters = (state)=>state.personnel.filters;\nconst selectPersonnelViewMode = (state)=>state.personnel.viewMode;\nconst selectPersonnelLoading = (state)=>state.personnel.loading;\nconst selectPersonnelOperationLoading = (state)=>state.personnel.operationLoading;\nconst selectPersonnelError = (state)=>state.personnel.error;\n// Export reducer\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (personnelSlice.reducer);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/redux/slices/personnelSlice.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/redux/slices/settingsSlice.ts":
/*!***********************************************!*\
  !*** ./src/lib/redux/slices/settingsSlice.ts ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearSettingsError: () => (/* binding */ clearSettingsError),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   loadSettings: () => (/* binding */ loadSettings),\n/* harmony export */   resetSettings: () => (/* binding */ resetSettings),\n/* harmony export */   saveSystemSettings: () => (/* binding */ saveSystemSettings),\n/* harmony export */   saveUserPreferences: () => (/* binding */ saveUserPreferences),\n/* harmony export */   selectFeatureFlag: () => (/* binding */ selectFeatureFlag),\n/* harmony export */   selectFeatureFlags: () => (/* binding */ selectFeatureFlags),\n/* harmony export */   selectSettingsError: () => (/* binding */ selectSettingsError),\n/* harmony export */   selectSettingsLastUpdated: () => (/* binding */ selectSettingsLastUpdated),\n/* harmony export */   selectSettingsLoading: () => (/* binding */ selectSettingsLoading),\n/* harmony export */   selectSystemSettings: () => (/* binding */ selectSystemSettings),\n/* harmony export */   selectUserPreferences: () => (/* binding */ selectUserPreferences),\n/* harmony export */   setFeatureFlags: () => (/* binding */ setFeatureFlags),\n/* harmony export */   setSystemSettings: () => (/* binding */ setSystemSettings),\n/* harmony export */   setUserPreferences: () => (/* binding */ setUserPreferences),\n/* harmony export */   toggleFeatureFlag: () => (/* binding */ toggleFeatureFlag),\n/* harmony export */   updateFeatureFlags: () => (/* binding */ updateFeatureFlags)\n/* harmony export */ });\n/* harmony import */ var _reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @reduxjs/toolkit */ \"(ssr)/./node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs\");\n/**\n * Settings Redux Slice\n * \n * Manages application settings including:\n * - User preferences\n * - System configuration\n * - Feature flags\n * - Application metadata\n */ \n// Initial state\nconst initialState = {\n    userPreferences: {\n        language: 'en',\n        timezone: 'Asia/Manila',\n        dateFormat: 'MM/DD/YYYY',\n        timeFormat: '12h',\n        itemsPerPage: 10,\n        defaultView: 'list',\n        enableNotifications: true,\n        enableSounds: true,\n        autoSave: true\n    },\n    systemSettings: {\n        appName: 'LGU Project App',\n        appVersion: '1.0.0',\n        maintenanceMode: false,\n        allowRegistration: false,\n        maxFileUploadSize: 10485760,\n        supportedFileTypes: [\n            '.jpg',\n            '.jpeg',\n            '.png',\n            '.pdf',\n            '.doc',\n            '.docx'\n        ],\n        sessionTimeout: 3600000,\n        passwordPolicy: {\n            minLength: 8,\n            requireUppercase: true,\n            requireLowercase: true,\n            requireNumbers: true,\n            requireSpecialChars: false\n        }\n    },\n    featureFlags: {\n        enableChatbot: true,\n        enableAnalytics: true,\n        enableExport: true,\n        enableImport: true,\n        enableBulkOperations: true,\n        enableAdvancedSearch: true,\n        enableReports: true,\n        enableNotifications: true\n    },\n    loading: false,\n    error: null,\n    lastUpdated: null\n};\n// Async thunks\nconst loadSettings = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.createAsyncThunk)('settings/loadSettings', async (_, { rejectWithValue })=>{\n    try {\n        // Try to load from localStorage first\n        const savedPreferences = localStorage.getItem('userPreferences');\n        const savedSystemSettings = localStorage.getItem('systemSettings');\n        const savedFeatureFlags = localStorage.getItem('featureFlags');\n        const settings = {\n            userPreferences: savedPreferences ? JSON.parse(savedPreferences) : initialState.userPreferences,\n            systemSettings: savedSystemSettings ? JSON.parse(savedSystemSettings) : initialState.systemSettings,\n            featureFlags: savedFeatureFlags ? JSON.parse(savedFeatureFlags) : initialState.featureFlags\n        };\n        return settings;\n    } catch (error) {\n        const errorMessage = error instanceof Error ? error.message : 'Failed to load settings';\n        return rejectWithValue(errorMessage);\n    }\n});\nconst saveUserPreferences = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.createAsyncThunk)('settings/saveUserPreferences', async (preferences, { getState, rejectWithValue })=>{\n    try {\n        const state = getState();\n        const updatedPreferences = {\n            ...state.settings.userPreferences,\n            ...preferences\n        };\n        // Save to localStorage\n        localStorage.setItem('userPreferences', JSON.stringify(updatedPreferences));\n        // In a real app, you might also save to the server\n        // const response = await fetch('/api/settings/preferences', {\n        //   method: 'PUT',\n        //   headers: { 'Content-Type': 'application/json' },\n        //   body: JSON.stringify(updatedPreferences),\n        // })\n        return updatedPreferences;\n    } catch (error) {\n        const errorMessage = error instanceof Error ? error.message : 'Failed to save user preferences';\n        return rejectWithValue(errorMessage);\n    }\n});\nconst saveSystemSettings = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.createAsyncThunk)('settings/saveSystemSettings', async (settings, { getState, rejectWithValue })=>{\n    try {\n        const state = getState();\n        const updatedSettings = {\n            ...state.settings.systemSettings,\n            ...settings\n        };\n        // Save to localStorage\n        localStorage.setItem('systemSettings', JSON.stringify(updatedSettings));\n        return updatedSettings;\n    } catch (error) {\n        const errorMessage = error instanceof Error ? error.message : 'Failed to save system settings';\n        return rejectWithValue(errorMessage);\n    }\n});\nconst updateFeatureFlags = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.createAsyncThunk)('settings/updateFeatureFlags', async (flags, { getState, rejectWithValue })=>{\n    try {\n        const state = getState();\n        const updatedFlags = {\n            ...state.settings.featureFlags,\n            ...flags\n        };\n        // Save to localStorage\n        localStorage.setItem('featureFlags', JSON.stringify(updatedFlags));\n        return updatedFlags;\n    } catch (error) {\n        const errorMessage = error instanceof Error ? error.message : 'Failed to update feature flags';\n        return rejectWithValue(errorMessage);\n    }\n});\n// Settings slice\nconst settingsSlice = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.createSlice)({\n    name: 'settings',\n    initialState,\n    reducers: {\n        setUserPreferences: (state, action)=>{\n            state.userPreferences = {\n                ...state.userPreferences,\n                ...action.payload\n            };\n        },\n        setSystemSettings: (state, action)=>{\n            state.systemSettings = {\n                ...state.systemSettings,\n                ...action.payload\n            };\n        },\n        setFeatureFlags: (state, action)=>{\n            state.featureFlags = {\n                ...state.featureFlags,\n                ...action.payload\n            };\n        },\n        toggleFeatureFlag: (state, action)=>{\n            state.featureFlags[action.payload] = !state.featureFlags[action.payload];\n        },\n        clearSettingsError: (state)=>{\n            state.error = null;\n        },\n        resetSettings: ()=>{\n            return initialState;\n        }\n    },\n    extraReducers: (builder)=>{\n        // Load settings\n        builder.addCase(loadSettings.pending, (state)=>{\n            state.loading = true;\n            state.error = null;\n        }).addCase(loadSettings.fulfilled, (state, action)=>{\n            state.loading = false;\n            state.userPreferences = action.payload.userPreferences;\n            state.systemSettings = action.payload.systemSettings;\n            state.featureFlags = action.payload.featureFlags;\n            state.lastUpdated = new Date().toISOString();\n            state.error = null;\n        }).addCase(loadSettings.rejected, (state, action)=>{\n            state.loading = false;\n            state.error = action.payload;\n        });\n        // Save user preferences\n        builder.addCase(saveUserPreferences.pending, (state)=>{\n            state.loading = true;\n            state.error = null;\n        }).addCase(saveUserPreferences.fulfilled, (state, action)=>{\n            state.loading = false;\n            state.userPreferences = action.payload;\n            state.lastUpdated = new Date().toISOString();\n            state.error = null;\n        }).addCase(saveUserPreferences.rejected, (state, action)=>{\n            state.loading = false;\n            state.error = action.payload;\n        });\n        // Save system settings\n        builder.addCase(saveSystemSettings.pending, (state)=>{\n            state.loading = true;\n            state.error = null;\n        }).addCase(saveSystemSettings.fulfilled, (state, action)=>{\n            state.loading = false;\n            state.systemSettings = action.payload;\n            state.lastUpdated = new Date().toISOString();\n            state.error = null;\n        }).addCase(saveSystemSettings.rejected, (state, action)=>{\n            state.loading = false;\n            state.error = action.payload;\n        });\n        // Update feature flags\n        builder.addCase(updateFeatureFlags.pending, (state)=>{\n            state.loading = true;\n            state.error = null;\n        }).addCase(updateFeatureFlags.fulfilled, (state, action)=>{\n            state.loading = false;\n            state.featureFlags = action.payload;\n            state.lastUpdated = new Date().toISOString();\n            state.error = null;\n        }).addCase(updateFeatureFlags.rejected, (state, action)=>{\n            state.loading = false;\n            state.error = action.payload;\n        });\n    }\n});\n// Export actions\nconst { setUserPreferences, setSystemSettings, setFeatureFlags, toggleFeatureFlag, clearSettingsError, resetSettings } = settingsSlice.actions;\n// Export selectors\nconst selectUserPreferences = (state)=>state.settings.userPreferences;\nconst selectSystemSettings = (state)=>state.settings.systemSettings;\nconst selectFeatureFlags = (state)=>state.settings.featureFlags;\nconst selectSettingsLoading = (state)=>state.settings.loading;\nconst selectSettingsError = (state)=>state.settings.error;\nconst selectSettingsLastUpdated = (state)=>state.settings.lastUpdated;\n// Feature flag selectors\nconst selectFeatureFlag = (flag)=>(state)=>state.settings.featureFlags[flag];\n// Export reducer\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (settingsSlice.reducer);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/redux/slices/settingsSlice.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/redux/slices/uiSlice.ts":
/*!*****************************************!*\
  !*** ./src/lib/redux/slices/uiSlice.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addBreadcrumb: () => (/* binding */ addBreadcrumb),\n/* harmony export */   addNotification: () => (/* binding */ addNotification),\n/* harmony export */   clearAllNotifications: () => (/* binding */ clearAllNotifications),\n/* harmony export */   clearBreadcrumbs: () => (/* binding */ clearBreadcrumbs),\n/* harmony export */   clearError: () => (/* binding */ clearError),\n/* harmony export */   closeAllModals: () => (/* binding */ closeAllModals),\n/* harmony export */   closeModal: () => (/* binding */ closeModal),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   openModal: () => (/* binding */ openModal),\n/* harmony export */   removeModal: () => (/* binding */ removeModal),\n/* harmony export */   removeNotification: () => (/* binding */ removeNotification),\n/* harmony export */   resetUIState: () => (/* binding */ resetUIState),\n/* harmony export */   selectBreadcrumbs: () => (/* binding */ selectBreadcrumbs),\n/* harmony export */   selectErrorMessage: () => (/* binding */ selectErrorMessage),\n/* harmony export */   selectGlobalLoading: () => (/* binding */ selectGlobalLoading),\n/* harmony export */   selectGlobalSearchOpen: () => (/* binding */ selectGlobalSearchOpen),\n/* harmony export */   selectGlobalSearchQuery: () => (/* binding */ selectGlobalSearchQuery),\n/* harmony export */   selectHasError: () => (/* binding */ selectHasError),\n/* harmony export */   selectLayoutDensity: () => (/* binding */ selectLayoutDensity),\n/* harmony export */   selectModalById: () => (/* binding */ selectModalById),\n/* harmony export */   selectModals: () => (/* binding */ selectModals),\n/* harmony export */   selectNotifications: () => (/* binding */ selectNotifications),\n/* harmony export */   selectPageTitle: () => (/* binding */ selectPageTitle),\n/* harmony export */   selectSidebarCollapsed: () => (/* binding */ selectSidebarCollapsed),\n/* harmony export */   selectSidebarOpen: () => (/* binding */ selectSidebarOpen),\n/* harmony export */   selectTheme: () => (/* binding */ selectTheme),\n/* harmony export */   setBreadcrumbs: () => (/* binding */ setBreadcrumbs),\n/* harmony export */   setError: () => (/* binding */ setError),\n/* harmony export */   setGlobalLoading: () => (/* binding */ setGlobalLoading),\n/* harmony export */   setGlobalSearchOpen: () => (/* binding */ setGlobalSearchOpen),\n/* harmony export */   setGlobalSearchQuery: () => (/* binding */ setGlobalSearchQuery),\n/* harmony export */   setLayoutDensity: () => (/* binding */ setLayoutDensity),\n/* harmony export */   setPageTitle: () => (/* binding */ setPageTitle),\n/* harmony export */   setSidebarCollapsed: () => (/* binding */ setSidebarCollapsed),\n/* harmony export */   setSidebarOpen: () => (/* binding */ setSidebarOpen),\n/* harmony export */   setTheme: () => (/* binding */ setTheme),\n/* harmony export */   toggleGlobalSearch: () => (/* binding */ toggleGlobalSearch),\n/* harmony export */   toggleSidebar: () => (/* binding */ toggleSidebar),\n/* harmony export */   toggleSidebarCollapsed: () => (/* binding */ toggleSidebarCollapsed)\n/* harmony export */ });\n/* harmony import */ var _reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @reduxjs/toolkit */ \"(ssr)/./node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs\");\n/**\n * UI Redux Slice\n * \n * Manages global UI state including:\n * - Modal states\n * - Loading indicators\n * - Notifications/alerts\n * - Sidebar state\n * - Theme preferences\n * - Global UI preferences\n */ \n// Initial state\nconst initialState = {\n    globalLoading: false,\n    sidebarOpen: true,\n    sidebarCollapsed: false,\n    modals: [],\n    notifications: [],\n    theme: 'light',\n    layoutDensity: 'comfortable',\n    globalSearchOpen: false,\n    globalSearchQuery: '',\n    breadcrumbs: [],\n    pageTitle: 'LGU Project App',\n    hasError: false,\n    errorMessage: null\n};\n// UI slice\nconst uiSlice = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.createSlice)({\n    name: 'ui',\n    initialState,\n    reducers: {\n        // Global loading\n        setGlobalLoading: (state, action)=>{\n            state.globalLoading = action.payload;\n        },\n        // Sidebar\n        setSidebarOpen: (state, action)=>{\n            state.sidebarOpen = action.payload;\n        },\n        setSidebarCollapsed: (state, action)=>{\n            state.sidebarCollapsed = action.payload;\n        },\n        toggleSidebar: (state)=>{\n            state.sidebarOpen = !state.sidebarOpen;\n        },\n        toggleSidebarCollapsed: (state)=>{\n            state.sidebarCollapsed = !state.sidebarCollapsed;\n        },\n        // Modals\n        openModal: (state, action)=>{\n            const existingModal = state.modals.find((m)=>m.id === action.payload.id);\n            if (existingModal) {\n                existingModal.isOpen = true;\n                existingModal.data = action.payload.data;\n                existingModal.props = action.payload.props;\n            } else {\n                state.modals.push({\n                    ...action.payload,\n                    isOpen: true\n                });\n            }\n        },\n        closeModal: (state, action)=>{\n            const modal = state.modals.find((m)=>m.id === action.payload);\n            if (modal) {\n                modal.isOpen = false;\n            }\n        },\n        closeAllModals: (state)=>{\n            state.modals.forEach((modal)=>{\n                modal.isOpen = false;\n            });\n        },\n        removeModal: (state, action)=>{\n            state.modals = state.modals.filter((m)=>m.id !== action.payload);\n        },\n        // Notifications\n        addNotification: (state, action)=>{\n            const notification = {\n                ...action.payload,\n                id: Date.now().toString() + Math.random().toString(36).substr(2, 9),\n                timestamp: Date.now()\n            };\n            state.notifications.push(notification);\n        },\n        removeNotification: (state, action)=>{\n            state.notifications = state.notifications.filter((n)=>n.id !== action.payload);\n        },\n        clearAllNotifications: (state)=>{\n            state.notifications = [];\n        },\n        // Theme\n        setTheme: (state, action)=>{\n            state.theme = action.payload;\n        },\n        // Layout\n        setLayoutDensity: (state, action)=>{\n            state.layoutDensity = action.payload;\n        },\n        // Global search\n        setGlobalSearchOpen: (state, action)=>{\n            state.globalSearchOpen = action.payload;\n        },\n        setGlobalSearchQuery: (state, action)=>{\n            state.globalSearchQuery = action.payload;\n        },\n        toggleGlobalSearch: (state)=>{\n            state.globalSearchOpen = !state.globalSearchOpen;\n        },\n        // Breadcrumbs\n        setBreadcrumbs: (state, action)=>{\n            state.breadcrumbs = action.payload;\n        },\n        addBreadcrumb: (state, action)=>{\n            state.breadcrumbs.push(action.payload);\n        },\n        clearBreadcrumbs: (state)=>{\n            state.breadcrumbs = [];\n        },\n        // Page title\n        setPageTitle: (state, action)=>{\n            state.pageTitle = action.payload;\n        },\n        // Error boundary\n        setError: (state, action)=>{\n            state.hasError = action.payload.hasError;\n            state.errorMessage = action.payload.message || null;\n        },\n        clearError: (state)=>{\n            state.hasError = false;\n            state.errorMessage = null;\n        },\n        // Reset UI state\n        resetUIState: ()=>{\n            return initialState;\n        }\n    }\n});\n// Export actions\nconst { setGlobalLoading, setSidebarOpen, setSidebarCollapsed, toggleSidebar, toggleSidebarCollapsed, openModal, closeModal, closeAllModals, removeModal, addNotification, removeNotification, clearAllNotifications, setTheme, setLayoutDensity, setGlobalSearchOpen, setGlobalSearchQuery, toggleGlobalSearch, setBreadcrumbs, addBreadcrumb, clearBreadcrumbs, setPageTitle, setError, clearError, resetUIState } = uiSlice.actions;\n// Export selectors\nconst selectGlobalLoading = (state)=>state.ui.globalLoading;\nconst selectSidebarOpen = (state)=>state.ui.sidebarOpen;\nconst selectSidebarCollapsed = (state)=>state.ui.sidebarCollapsed;\nconst selectModals = (state)=>state.ui.modals;\nconst selectModalById = (id)=>(state)=>state.ui.modals.find((m)=>m.id === id);\nconst selectNotifications = (state)=>state.ui.notifications;\nconst selectTheme = (state)=>state.ui.theme;\nconst selectLayoutDensity = (state)=>state.ui.layoutDensity;\nconst selectGlobalSearchOpen = (state)=>state.ui.globalSearchOpen;\nconst selectGlobalSearchQuery = (state)=>state.ui.globalSearchQuery;\nconst selectBreadcrumbs = (state)=>state.ui.breadcrumbs;\nconst selectPageTitle = (state)=>state.ui.pageTitle;\nconst selectHasError = (state)=>state.ui.hasError;\nconst selectErrorMessage = (state)=>state.ui.errorMessage;\n// Export reducer\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (uiSlice.reducer);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/redux/slices/uiSlice.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/redux/slices/usersSlice.ts":
/*!********************************************!*\
  !*** ./src/lib/redux/slices/usersSlice.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearUsersError: () => (/* binding */ clearUsersError),\n/* harmony export */   createUser: () => (/* binding */ createUser),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   deleteUser: () => (/* binding */ deleteUser),\n/* harmony export */   fetchUserById: () => (/* binding */ fetchUserById),\n/* harmony export */   fetchUsers: () => (/* binding */ fetchUsers),\n/* harmony export */   resetUsersState: () => (/* binding */ resetUsersState),\n/* harmony export */   selectSelectedUser: () => (/* binding */ selectSelectedUser),\n/* harmony export */   selectUsers: () => (/* binding */ selectUsers),\n/* harmony export */   selectUsersError: () => (/* binding */ selectUsersError),\n/* harmony export */   selectUsersFilters: () => (/* binding */ selectUsersFilters),\n/* harmony export */   selectUsersLoading: () => (/* binding */ selectUsersLoading),\n/* harmony export */   selectUsersOperationLoading: () => (/* binding */ selectUsersOperationLoading),\n/* harmony export */   selectUsersPagination: () => (/* binding */ selectUsersPagination),\n/* harmony export */   setSelectedUser: () => (/* binding */ setSelectedUser),\n/* harmony export */   setUsersFilters: () => (/* binding */ setUsersFilters),\n/* harmony export */   setUsersPagination: () => (/* binding */ setUsersPagination),\n/* harmony export */   updateUser: () => (/* binding */ updateUser)\n/* harmony export */ });\n/* harmony import */ var _reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @reduxjs/toolkit */ \"(ssr)/./node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs\");\n/**\n * Users Redux Slice\n * \n * Manages user data state including:\n * - User list with pagination\n * - User CRUD operations\n * - User roles and permissions\n * - Loading states and error handling\n */ \n// Initial state\nconst initialState = {\n    users: [],\n    selectedUser: null,\n    pagination: {\n        page: 1,\n        limit: 10,\n        total: 0,\n        pages: 0\n    },\n    filters: {\n        search: '',\n        sortBy: 'name_asc'\n    },\n    loading: false,\n    error: null,\n    operationLoading: false\n};\n// Async thunks\nconst fetchUsers = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.createAsyncThunk)('users/fetchUsers', async (params = {}, { rejectWithValue })=>{\n    try {\n        const searchParams = new URLSearchParams({\n            page: (params.page || 1).toString(),\n            limit: (params.limit || 10).toString(),\n            ...params.search && {\n                search: params.search\n            },\n            ...params.role && {\n                role: params.role\n            },\n            ...params.status && {\n                status: params.status\n            },\n            sort: params.sortBy || 'name_asc'\n        });\n        const response = await fetch(`/api/users?${searchParams}`);\n        if (!response.ok) {\n            throw new Error('Failed to fetch users');\n        }\n        const data = await response.json();\n        return {\n            users: data.users,\n            pagination: data.pagination\n        };\n    } catch (error) {\n        const errorMessage = error instanceof Error ? error.message : 'Failed to fetch users';\n        return rejectWithValue(errorMessage);\n    }\n});\nconst fetchUserById = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.createAsyncThunk)('users/fetchUserById', async (id, { rejectWithValue })=>{\n    try {\n        const response = await fetch(`/api/users/${id}`);\n        if (!response.ok) {\n            throw new Error('Failed to fetch user');\n        }\n        const data = await response.json();\n        return data.user;\n    } catch (error) {\n        const errorMessage = error instanceof Error ? error.message : 'Failed to fetch user';\n        return rejectWithValue(errorMessage);\n    }\n});\nconst createUser = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.createAsyncThunk)('users/createUser', async (userData, { rejectWithValue })=>{\n    try {\n        const response = await fetch('/api/users', {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify(userData)\n        });\n        if (!response.ok) {\n            throw new Error('Failed to create user');\n        }\n        const data = await response.json();\n        return data.user;\n    } catch (error) {\n        const errorMessage = error instanceof Error ? error.message : 'Failed to create user';\n        return rejectWithValue(errorMessage);\n    }\n});\nconst updateUser = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.createAsyncThunk)('users/updateUser', async ({ id, ...userData }, { rejectWithValue })=>{\n    try {\n        const response = await fetch(`/api/users/${id}`, {\n            method: 'PUT',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify(userData)\n        });\n        if (!response.ok) {\n            throw new Error('Failed to update user');\n        }\n        const data = await response.json();\n        return data.user;\n    } catch (error) {\n        const errorMessage = error instanceof Error ? error.message : 'Failed to update user';\n        return rejectWithValue(errorMessage);\n    }\n});\nconst deleteUser = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.createAsyncThunk)('users/deleteUser', async (id, { rejectWithValue })=>{\n    try {\n        const response = await fetch(`/api/users/${id}`, {\n            method: 'DELETE'\n        });\n        if (!response.ok) {\n            throw new Error('Failed to delete user');\n        }\n        return id;\n    } catch (error) {\n        const errorMessage = error instanceof Error ? error.message : 'Failed to delete user';\n        return rejectWithValue(errorMessage);\n    }\n});\n// Users slice\nconst usersSlice = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.createSlice)({\n    name: 'users',\n    initialState,\n    reducers: {\n        setSelectedUser: (state, action)=>{\n            state.selectedUser = action.payload;\n        },\n        setUsersFilters: (state, action)=>{\n            state.filters = {\n                ...state.filters,\n                ...action.payload\n            };\n        },\n        setUsersPagination: (state, action)=>{\n            state.pagination = {\n                ...state.pagination,\n                ...action.payload\n            };\n        },\n        clearUsersError: (state)=>{\n            state.error = null;\n        },\n        resetUsersState: ()=>{\n            return initialState;\n        }\n    },\n    extraReducers: (builder)=>{\n        // Fetch users\n        builder.addCase(fetchUsers.pending, (state)=>{\n            state.loading = true;\n            state.error = null;\n        }).addCase(fetchUsers.fulfilled, (state, action)=>{\n            state.loading = false;\n            state.users = action.payload.users;\n            state.pagination = action.payload.pagination;\n            state.error = null;\n        }).addCase(fetchUsers.rejected, (state, action)=>{\n            state.loading = false;\n            state.error = action.payload;\n        });\n        // Fetch user by ID\n        builder.addCase(fetchUserById.pending, (state)=>{\n            state.loading = true;\n            state.error = null;\n        }).addCase(fetchUserById.fulfilled, (state, action)=>{\n            state.loading = false;\n            state.selectedUser = action.payload;\n            state.error = null;\n        }).addCase(fetchUserById.rejected, (state, action)=>{\n            state.loading = false;\n            state.error = action.payload;\n        });\n        // Create user\n        builder.addCase(createUser.pending, (state)=>{\n            state.operationLoading = true;\n            state.error = null;\n        }).addCase(createUser.fulfilled, (state, action)=>{\n            state.operationLoading = false;\n            state.users.unshift(action.payload);\n            state.pagination.total += 1;\n            state.error = null;\n        }).addCase(createUser.rejected, (state, action)=>{\n            state.operationLoading = false;\n            state.error = action.payload;\n        });\n        // Update user\n        builder.addCase(updateUser.pending, (state)=>{\n            state.operationLoading = true;\n            state.error = null;\n        }).addCase(updateUser.fulfilled, (state, action)=>{\n            state.operationLoading = false;\n            const index = state.users.findIndex((u)=>u.id === action.payload.id);\n            if (index !== -1) {\n                state.users[index] = action.payload;\n            }\n            if (state.selectedUser?.id === action.payload.id) {\n                state.selectedUser = action.payload;\n            }\n            state.error = null;\n        }).addCase(updateUser.rejected, (state, action)=>{\n            state.operationLoading = false;\n            state.error = action.payload;\n        });\n        // Delete user\n        builder.addCase(deleteUser.pending, (state)=>{\n            state.operationLoading = true;\n            state.error = null;\n        }).addCase(deleteUser.fulfilled, (state, action)=>{\n            state.operationLoading = false;\n            state.users = state.users.filter((u)=>u.id !== action.payload);\n            state.pagination.total -= 1;\n            if (state.selectedUser?.id === action.payload) {\n                state.selectedUser = null;\n            }\n            state.error = null;\n        }).addCase(deleteUser.rejected, (state, action)=>{\n            state.operationLoading = false;\n            state.error = action.payload;\n        });\n    }\n});\n// Export actions\nconst { setSelectedUser, setUsersFilters, setUsersPagination, clearUsersError, resetUsersState } = usersSlice.actions;\n// Export selectors\nconst selectUsers = (state)=>state.users.users;\nconst selectSelectedUser = (state)=>state.users.selectedUser;\nconst selectUsersPagination = (state)=>state.users.pagination;\nconst selectUsersFilters = (state)=>state.users.filters;\nconst selectUsersLoading = (state)=>state.users.loading;\nconst selectUsersOperationLoading = (state)=>state.users.operationLoading;\nconst selectUsersError = (state)=>state.users.error;\n// Export reducer\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (usersSlice.reducer);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/redux/slices/usersSlice.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/store.ts":
/*!**************************!*\
  !*** ./src/lib/store.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   store: () => (/* binding */ store),\n/* harmony export */   useAppDispatch: () => (/* binding */ useAppDispatch),\n/* harmony export */   useAppSelector: () => (/* binding */ useAppSelector)\n/* harmony export */ });\n/* harmony import */ var _reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @reduxjs/toolkit */ \"(ssr)/./node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs\");\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-redux */ \"(ssr)/./node_modules/react-redux/dist/react-redux.mjs\");\n/* harmony import */ var _redux_slices_authSlice__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./redux/slices/authSlice */ \"(ssr)/./src/lib/redux/slices/authSlice.ts\");\n/* harmony import */ var _redux_slices_personnelSlice__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./redux/slices/personnelSlice */ \"(ssr)/./src/lib/redux/slices/personnelSlice.ts\");\n/* harmony import */ var _redux_slices_usersSlice__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./redux/slices/usersSlice */ \"(ssr)/./src/lib/redux/slices/usersSlice.ts\");\n/* harmony import */ var _redux_slices_uiSlice__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./redux/slices/uiSlice */ \"(ssr)/./src/lib/redux/slices/uiSlice.ts\");\n/* harmony import */ var _redux_slices_settingsSlice__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./redux/slices/settingsSlice */ \"(ssr)/./src/lib/redux/slices/settingsSlice.ts\");\n/**\n * Redux Store Configuration\n * \n * This file configures the Redux store with Redux Toolkit, including:\n * - Store setup with proper middleware\n * - Root reducer configuration\n * - TypeScript types for the store\n * - Redux DevTools integration\n */ \n\n// Import reducers (will be created next)\n\n\n\n\n\n// Configure the store\nconst store = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_5__.configureStore)({\n    reducer: {\n        auth: _redux_slices_authSlice__WEBPACK_IMPORTED_MODULE_0__[\"default\"],\n        personnel: _redux_slices_personnelSlice__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n        users: _redux_slices_usersSlice__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        ui: _redux_slices_uiSlice__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        settings: _redux_slices_settingsSlice__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n    },\n    middleware: (getDefaultMiddleware)=>getDefaultMiddleware({\n            serializableCheck: {\n                // Ignore these action types for serializable check\n                ignoredActions: [\n                    'persist/PERSIST',\n                    'persist/REHYDRATE'\n                ],\n                // Ignore these field paths in all actions\n                ignoredActionsPaths: [\n                    'meta.arg',\n                    'payload.timestamp'\n                ],\n                // Ignore these paths in the state\n                ignoredPaths: [\n                    'items.dates'\n                ]\n            }\n        }),\n    devTools: \"development\" !== 'production'\n});\n// Export typed hooks for use throughout the app\nconst useAppDispatch = ()=>(0,react_redux__WEBPACK_IMPORTED_MODULE_6__.useDispatch)();\nconst useAppSelector = react_redux__WEBPACK_IMPORTED_MODULE_6__.useSelector;\n// Export store instance\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (store);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/store.ts\n");

/***/ }),

/***/ "(ssr)/./src/utils/supabase/client.ts":
/*!**************************************!*\
  !*** ./src/utils/supabase/client.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createClient: () => (/* binding */ createClient),\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(ssr)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/**\n * Supabase Client for Client Components\n * \n * This client is used in Client Components that run in the browser.\n * It handles authentication state, session management, and real-time subscriptions.\n * \n * Usage:\n * ```typescript\n * import { createClient } from '@/utils/supabase/client'\n * \n * const supabase = createClient()\n * const { data: { user } } = await supabase.auth.getUser()\n * ```\n */ \nfunction createClient() {\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createBrowserClient)(\"https://lkolpgpmdculqqfqyzaf.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imxrb2xwZ3BtZGN1bHFxZnF5emFmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk0ODE5ODcsImV4cCI6MjA2NTA1Nzk4N30.MRwyyo6wLKs2HWa4tQdfBPEq3mDee19lckU3MnVyWhU\");\n}\n// Export a default instance for convenience\nconst supabase = createClient();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvdXRpbHMvc3VwYWJhc2UvY2xpZW50LnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBOzs7Ozs7Ozs7Ozs7O0NBYUMsR0FFa0Q7QUFHNUMsU0FBU0M7SUFDZCxPQUFPRCxrRUFBbUJBLENBQ3hCRSwwQ0FBb0MsRUFDcENBLGtOQUF5QztBQUU3QztBQUVBLDRDQUE0QztBQUNyQyxNQUFNSSxXQUFXTCxlQUFjIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXERBQkJJRVxcT25lRHJpdmVcXERlc2t0b3BcXGxndS1wcm9qZWN0LWFwcFxcc3JjXFx1dGlsc1xcc3VwYWJhc2VcXGNsaWVudC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIFN1cGFiYXNlIENsaWVudCBmb3IgQ2xpZW50IENvbXBvbmVudHNcbiAqIFxuICogVGhpcyBjbGllbnQgaXMgdXNlZCBpbiBDbGllbnQgQ29tcG9uZW50cyB0aGF0IHJ1biBpbiB0aGUgYnJvd3Nlci5cbiAqIEl0IGhhbmRsZXMgYXV0aGVudGljYXRpb24gc3RhdGUsIHNlc3Npb24gbWFuYWdlbWVudCwgYW5kIHJlYWwtdGltZSBzdWJzY3JpcHRpb25zLlxuICogXG4gKiBVc2FnZTpcbiAqIGBgYHR5cGVzY3JpcHRcbiAqIGltcG9ydCB7IGNyZWF0ZUNsaWVudCB9IGZyb20gJ0AvdXRpbHMvc3VwYWJhc2UvY2xpZW50J1xuICogXG4gKiBjb25zdCBzdXBhYmFzZSA9IGNyZWF0ZUNsaWVudCgpXG4gKiBjb25zdCB7IGRhdGE6IHsgdXNlciB9IH0gPSBhd2FpdCBzdXBhYmFzZS5hdXRoLmdldFVzZXIoKVxuICogYGBgXG4gKi9cblxuaW1wb3J0IHsgY3JlYXRlQnJvd3NlckNsaWVudCB9IGZyb20gJ0BzdXBhYmFzZS9zc3InXG5pbXBvcnQgeyBEYXRhYmFzZSB9IGZyb20gJ0AvbGliL2RhdGFiYXNlLnR5cGVzJ1xuXG5leHBvcnQgZnVuY3Rpb24gY3JlYXRlQ2xpZW50KCkge1xuICByZXR1cm4gY3JlYXRlQnJvd3NlckNsaWVudDxEYXRhYmFzZT4oXG4gICAgcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfU1VQQUJBU0VfVVJMISxcbiAgICBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19TVVBBQkFTRV9BTk9OX0tFWSFcbiAgKVxufVxuXG4vLyBFeHBvcnQgYSBkZWZhdWx0IGluc3RhbmNlIGZvciBjb252ZW5pZW5jZVxuZXhwb3J0IGNvbnN0IHN1cGFiYXNlID0gY3JlYXRlQ2xpZW50KClcbiJdLCJuYW1lcyI6WyJjcmVhdGVCcm93c2VyQ2xpZW50IiwiY3JlYXRlQ2xpZW50IiwicHJvY2VzcyIsImVudiIsIk5FWFRfUFVCTElDX1NVUEFCQVNFX1VSTCIsIk5FWFRfUFVCTElDX1NVUEFCQVNFX0FOT05fS0VZIiwic3VwYWJhc2UiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/supabase/client.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/@reduxjs","vendor-chunks/lucide-react","vendor-chunks/react-redux","vendor-chunks/immer","vendor-chunks/reselect","vendor-chunks/redux","vendor-chunks/use-sync-external-store","vendor-chunks/@swc","vendor-chunks/redux-thunk","vendor-chunks/zod","vendor-chunks/@hookform","vendor-chunks/react-hook-form"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fauth%2Flogin%2Fpage&page=%2Fauth%2Flogin%2Fpage&appPaths=%2Fauth%2Flogin%2Fpage&pagePath=private-next-app-dir%2Fauth%2Flogin%2Fpage.tsx&appDir=C%3A%5CUsers%5CDABBIE%5COneDrive%5CDesktop%5Clgu-project-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDABBIE%5COneDrive%5CDesktop%5Clgu-project-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();