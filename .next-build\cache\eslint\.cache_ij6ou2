[{"C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\admin\\activity\\page.tsx": "1", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\admin\\analytics\\page.tsx": "2", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\admin\\archive\\page.tsx": "3", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\admin\\backup\\page.tsx": "4", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\admin\\calendar\\page.tsx": "5", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\admin\\charts\\page.tsx": "6", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\admin\\charts-demo\\page.tsx": "7", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\admin\\communications\\page.tsx": "8", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\admin\\database\\page.tsx": "9", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\admin\\departments\\page.tsx": "10", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\admin\\documents\\page.tsx": "11", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\admin\\email-test\\page.tsx": "12", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\admin\\files\\page.tsx": "13", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\admin\\filters\\page.tsx": "14", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\admin\\import-export\\page.tsx": "15", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\admin\\layout.tsx": "16", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\admin\\locations\\page.tsx": "17", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\admin\\media\\page.tsx": "18", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\admin\\messages\\page.tsx": "19", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\admin\\monitor\\page.tsx": "20", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\admin\\notifications\\page.tsx": "21", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\admin\\page.tsx": "22", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\admin\\personnel\\page.tsx": "23", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\admin\\personnel\\redux-example.tsx": "24", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\admin\\personnel\\[id]\\page.tsx": "25", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\admin\\print\\page.tsx": "26", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\admin\\reports\\page.tsx": "27", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\admin\\search\\page.tsx": "28", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\admin\\security\\page.tsx": "29", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\admin\\settings\\page.tsx": "30", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\admin\\statistics\\page.tsx": "31", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\admin\\users\\page.tsx": "32", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\api\\chat\\route.ts": "33", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\api\\check-demo-user\\route.ts": "34", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\api\\cloudinary\\media\\route.ts": "35", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\api\\cloudinary\\sync\\route.ts": "36", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\api\\cloudinary\\upload\\route.ts": "37", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\api\\cloudinary\\webhook\\route.ts": "38", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\api\\create-demo-user\\route.ts": "39", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\api\\debug-database\\route.ts": "40", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\api\\email\\send\\route.ts": "41", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\api\\email\\test\\route.ts": "42", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\api\\fix-database\\route.ts": "43", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\api\\fix-demo-user\\route.ts": "44", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\api\\personnel\\route.ts": "45", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\api\\personnel\\[id]\\route.ts": "46", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\api\\setup-media-db\\route.ts": "47", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\api\\test-cloudinary\\route.ts": "48", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\api\\users\\route.ts": "49", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\api\\users\\[id]\\route.ts": "50", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\auth\\auth-code-error\\page.tsx": "51", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\auth\\confirm\\route.ts": "52", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\auth\\login\\page.tsx": "53", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\auth\\register\\page.tsx": "54", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\direct-echarts-test\\page.tsx": "55", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\health\\page.tsx": "56", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\layout.tsx": "57", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\page.tsx": "58", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\quick-setup\\page.tsx": "59", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\test-cloudinary\\page.tsx": "60", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\test-cloudinary-image\\page.tsx": "61", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\test-echarts\\page.tsx": "62", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\test-media\\page.tsx": "63", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\test-media-sync\\page.tsx": "64", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\test-redux\\page.tsx": "65", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\components\\auth\\AuthGuard.tsx": "66", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\components\\charts\\BarChart.tsx": "67", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\components\\charts\\ChartWrapper.tsx": "68", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\components\\charts\\index.ts": "69", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\components\\charts\\LineChart.tsx": "70", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\components\\charts\\PieChart.tsx": "71", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\components\\chatbot\\ChatBot.tsx": "72", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\components\\chatbot\\ChatBubble.tsx": "73", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\components\\chatbot\\ChatInterface.tsx": "74", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\components\\chatbot\\ChatMessage.tsx": "75", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\components\\ClientOnly.tsx": "76", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\components\\CloudinaryImage.tsx": "77", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\components\\CloudinaryUploadWidget.tsx": "78", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\components\\DeleteConfirmModal.tsx": "79", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\components\\DevIndicators.tsx": "80", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\components\\Layout\\DashboardLayout.tsx": "81", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\components\\Layout\\Header.tsx": "82", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\components\\Layout\\Sidebar.tsx": "83", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\components\\PersonnelDeleteModal.tsx": "84", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\components\\PersonnelModal.tsx": "85", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\components\\PersonnelViewModal.tsx": "86", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\components\\PlaceholderPage.tsx": "87", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\components\\providers\\ReduxProvider.tsx": "88", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\components\\providers\\SupabaseAuthProvider.tsx": "89", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\components\\UserModal.tsx": "90", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\components\\UserViewModal.tsx": "91", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\lib\\bidirectionalSyncService.ts": "92", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\lib\\chatbot-rules.ts": "93", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\lib\\cloudinary.ts": "94", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\lib\\cloudinaryClient.ts": "95", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\lib\\database.ts": "96", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\lib\\database.types.ts": "97", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\lib\\db.ts": "98", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\lib\\email-templates\\NotificationEmailTemplate.tsx": "99", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\lib\\email-templates\\SystemAlertEmailTemplate.tsx": "100", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\lib\\email-templates\\WelcomeEmailTemplate.tsx": "101", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\lib\\emailService.ts": "102", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\lib\\gemini.ts": "103", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\lib\\mediaLibraryService.ts": "104", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\lib\\mockData.ts": "105", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\lib\\redux\\hooks.ts": "106", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\lib\\redux\\slices\\authSlice.ts": "107", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\lib\\redux\\slices\\personnelSlice.ts": "108", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\lib\\redux\\slices\\settingsSlice.ts": "109", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\lib\\redux\\slices\\uiSlice.ts": "110", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\lib\\redux\\slices\\usersSlice.ts": "111", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\lib\\store.ts": "112", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\lib\\supabase.ts": "113", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\lib\\supabaseMediaService.ts": "114", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\lib\\supabaseService.ts": "115", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\lib\\supabaseTest.ts": "116", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\utils\\supabase\\client.ts": "117", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\utils\\supabase\\server.ts": "118", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\utils\\tailwind-health-check.ts": "119", "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\utils\\tailwind-optimizer.ts": "120"}, {"size": 656, "mtime": 1749707638031, "results": "121", "hashOfConfig": "122"}, {"size": 11745, "mtime": 1749486433370, "results": "123", "hashOfConfig": "122"}, {"size": 660, "mtime": 1749707671924, "results": "124", "hashOfConfig": "122"}, {"size": 668, "mtime": 1749707688782, "results": "125", "hashOfConfig": "122"}, {"size": 15450, "mtime": 1749486446262, "results": "126", "hashOfConfig": "122"}, {"size": 48509, "mtime": 1749890251682, "results": "127", "hashOfConfig": "122"}, {"size": 8516, "mtime": 1749887542098, "results": "128", "hashOfConfig": "122"}, {"size": 678, "mtime": 1749707704801, "results": "129", "hashOfConfig": "122"}, {"size": 672, "mtime": 1749707654913, "results": "130", "hashOfConfig": "122"}, {"size": 14723, "mtime": 1749486473262, "results": "131", "hashOfConfig": "122"}, {"size": 12383, "mtime": 1749634053933, "results": "132", "hashOfConfig": "122"}, {"size": 11425, "mtime": 1749887722729, "results": "133", "hashOfConfig": "122"}, {"size": 633, "mtime": 1749707725109, "results": "134", "hashOfConfig": "122"}, {"size": 653, "mtime": 1749707742374, "results": "135", "hashOfConfig": "122"}, {"size": 678, "mtime": 1749707761130, "results": "136", "hashOfConfig": "122"}, {"size": 387, "mtime": 1749715456631, "results": "137", "hashOfConfig": "122"}, {"size": 15019, "mtime": 1749486530990, "results": "138", "hashOfConfig": "122"}, {"size": 34708, "mtime": 1749883916118, "results": "139", "hashOfConfig": "122"}, {"size": 659, "mtime": 1749707778008, "results": "140", "hashOfConfig": "122"}, {"size": 653, "mtime": 1749707794001, "results": "141", "hashOfConfig": "122"}, {"size": 10808, "mtime": 1749486618971, "results": "142", "hashOfConfig": "122"}, {"size": 12426, "mtime": 1749485052458, "results": "143", "hashOfConfig": "122"}, {"size": 22702, "mtime": 1749634142988, "results": "144", "hashOfConfig": "122"}, {"size": 10727, "mtime": 1749722519125, "results": "145", "hashOfConfig": "122"}, {"size": 16316, "mtime": 1749560592182, "results": "146", "hashOfConfig": "122"}, {"size": 630, "mtime": 1749707810744, "results": "147", "hashOfConfig": "122"}, {"size": 12479, "mtime": 1749486697211, "results": "148", "hashOfConfig": "122"}, {"size": 645, "mtime": 1749707827577, "results": "149", "hashOfConfig": "122"}, {"size": 649, "mtime": 1749707854171, "results": "150", "hashOfConfig": "122"}, {"size": 21136, "mtime": 1749728255530, "results": "151", "hashOfConfig": "122"}, {"size": 15714, "mtime": 1749486761017, "results": "152", "hashOfConfig": "122"}, {"size": 11817, "mtime": 1749472882843, "results": "153", "hashOfConfig": "122"}, {"size": 3498, "mtime": 1749642964662, "results": "154", "hashOfConfig": "122"}, {"size": 1752, "mtime": 1749568335522, "results": "155", "hashOfConfig": "122"}, {"size": 9668, "mtime": 1749886027639, "results": "156", "hashOfConfig": "122"}, {"size": 5044, "mtime": 1749887746300, "results": "157", "hashOfConfig": "122"}, {"size": 7392, "mtime": 1749880295376, "results": "158", "hashOfConfig": "122"}, {"size": 7722, "mtime": 1749880321553, "results": "159", "hashOfConfig": "122"}, {"size": 2468, "mtime": 1749568206876, "results": "160", "hashOfConfig": "122"}, {"size": 7668, "mtime": 1749882398688, "results": "161", "hashOfConfig": "122"}, {"size": 5450, "mtime": 1749728126398, "results": "162", "hashOfConfig": "122"}, {"size": 5286, "mtime": 1749728155721, "results": "163", "hashOfConfig": "122"}, {"size": 3241, "mtime": 1749882411855, "results": "164", "hashOfConfig": "122"}, {"size": 2388, "mtime": 1749568415151, "results": "165", "hashOfConfig": "122"}, {"size": 4109, "mtime": 1749566375500, "results": "166", "hashOfConfig": "122"}, {"size": 4125, "mtime": 1749566249542, "results": "167", "hashOfConfig": "122"}, {"size": 15480, "mtime": 1749886288606, "results": "168", "hashOfConfig": "122"}, {"size": 5234, "mtime": 1749885952582, "results": "169", "hashOfConfig": "122"}, {"size": 2323, "mtime": 1749566828691, "results": "170", "hashOfConfig": "122"}, {"size": 3548, "mtime": 1749566551196, "results": "171", "hashOfConfig": "122"}, {"size": 2345, "mtime": 1749564663804, "results": "172", "hashOfConfig": "122"}, {"size": 1102, "mtime": 1749564647864, "results": "173", "hashOfConfig": "122"}, {"size": 7051, "mtime": 1749798753291, "results": "174", "hashOfConfig": "122"}, {"size": 9726, "mtime": 1749798824563, "results": "175", "hashOfConfig": "122"}, {"size": 10149, "mtime": 1749890073872, "results": "176", "hashOfConfig": "122"}, {"size": 3627, "mtime": 1749712925393, "results": "177", "hashOfConfig": "122"}, {"size": 1085, "mtime": 1749718579535, "results": "178", "hashOfConfig": "122"}, {"size": 13581, "mtime": 1749715414882, "results": "179", "hashOfConfig": "122"}, {"size": 10408, "mtime": 1749883086327, "results": "180", "hashOfConfig": "122"}, {"size": 13970, "mtime": 1749880794651, "results": "181", "hashOfConfig": "122"}, {"size": 4582, "mtime": 1749883110876, "results": "182", "hashOfConfig": "122"}, {"size": 2374, "mtime": 1749724864114, "results": "183", "hashOfConfig": "122"}, {"size": 9234, "mtime": 1749883924478, "results": "184", "hashOfConfig": "122"}, {"size": 11748, "mtime": 1749883416118, "results": "185", "hashOfConfig": "122"}, {"size": 8133, "mtime": 1749721292696, "results": "186", "hashOfConfig": "122"}, {"size": 3060, "mtime": 1749714204372, "results": "187", "hashOfConfig": "122"}, {"size": 2905, "mtime": 1749724495477, "results": "188", "hashOfConfig": "122"}, {"size": 2528, "mtime": 1749889549073, "results": "189", "hashOfConfig": "122"}, {"size": 1365, "mtime": 1749831318804, "results": "190", "hashOfConfig": "122"}, {"size": 2674, "mtime": 1749724473557, "results": "191", "hashOfConfig": "122"}, {"size": 2507, "mtime": 1749724514358, "results": "192", "hashOfConfig": "122"}, {"size": 9810, "mtime": 1749798874426, "results": "193", "hashOfConfig": "122"}, {"size": 2025, "mtime": 1749632352846, "results": "194", "hashOfConfig": "122"}, {"size": 7500, "mtime": 1749798914224, "results": "195", "hashOfConfig": "122"}, {"size": 1437, "mtime": 1749631586956, "results": "196", "hashOfConfig": "122"}, {"size": 884, "mtime": 1749798931111, "results": "197", "hashOfConfig": "122"}, {"size": 8820, "mtime": 1749885990851, "results": "198", "hashOfConfig": "122"}, {"size": 9330, "mtime": 1749886352380, "results": "199", "hashOfConfig": "122"}, {"size": 4236, "mtime": 1749396846674, "results": "200", "hashOfConfig": "122"}, {"size": 8389, "mtime": 1749705399834, "results": "201", "hashOfConfig": "122"}, {"size": 3501, "mtime": 1749564432524, "results": "202", "hashOfConfig": "122"}, {"size": 9814, "mtime": 1749634024677, "results": "203", "hashOfConfig": "122"}, {"size": 6798, "mtime": 1749728232098, "results": "204", "hashOfConfig": "122"}, {"size": 4909, "mtime": 1749396783782, "results": "205", "hashOfConfig": "122"}, {"size": 22208, "mtime": 1749883383049, "results": "206", "hashOfConfig": "122"}, {"size": 11112, "mtime": 1749397343244, "results": "207", "hashOfConfig": "122"}, {"size": 3685, "mtime": 1749707620787, "results": "208", "hashOfConfig": "122"}, {"size": 2346, "mtime": 1749718483768, "results": "209", "hashOfConfig": "122"}, {"size": 4010, "mtime": 1749715471569, "results": "210", "hashOfConfig": "122"}, {"size": 8665, "mtime": 1749396815873, "results": "211", "hashOfConfig": "122"}, {"size": 6294, "mtime": 1749394739230, "results": "212", "hashOfConfig": "122"}, {"size": 22916, "mtime": 1749885912307, "results": "213", "hashOfConfig": "122"}, {"size": 3679, "mtime": 1749631554284, "results": "214", "hashOfConfig": "122"}, {"size": 7728, "mtime": 1749885670566, "results": "215", "hashOfConfig": "122"}, {"size": 6883, "mtime": 1749884082998, "results": "216", "hashOfConfig": "122"}, {"size": 8649, "mtime": 1749558780571, "results": "217", "hashOfConfig": "122"}, {"size": 5477, "mtime": 1749483254195, "results": "218", "hashOfConfig": "122"}, {"size": 459, "mtime": 1749555717109, "results": "219", "hashOfConfig": "122"}, {"size": 4336, "mtime": 1749798984264, "results": "220", "hashOfConfig": "122"}, {"size": 8049, "mtime": 1749799000057, "results": "221", "hashOfConfig": "122"}, {"size": 4842, "mtime": 1749883506184, "results": "222", "hashOfConfig": "122"}, {"size": 9150, "mtime": 1749727995993, "results": "223", "hashOfConfig": "122"}, {"size": 1303, "mtime": 1749636049654, "results": "224", "hashOfConfig": "122"}, {"size": 19400, "mtime": 1749886114254, "results": "225", "hashOfConfig": "122"}, {"size": 10070, "mtime": 1749454435300, "results": "226", "hashOfConfig": "122"}, {"size": 15641, "mtime": 1749721189413, "results": "227", "hashOfConfig": "122"}, {"size": 6694, "mtime": 1749720713250, "results": "228", "hashOfConfig": "122"}, {"size": 10163, "mtime": 1749720789833, "results": "229", "hashOfConfig": "122"}, {"size": 9963, "mtime": 1749720950480, "results": "230", "hashOfConfig": "122"}, {"size": 7084, "mtime": 1749720885347, "results": "231", "hashOfConfig": "122"}, {"size": 9251, "mtime": 1749720858862, "results": "232", "hashOfConfig": "122"}, {"size": 1757, "mtime": 1749718243903, "results": "233", "hashOfConfig": "122"}, {"size": 2631, "mtime": 1749562937143, "results": "234", "hashOfConfig": "122"}, {"size": 10461, "mtime": 1749884197845, "results": "235", "hashOfConfig": "122"}, {"size": 18027, "mtime": 1749560532129, "results": "236", "hashOfConfig": "122"}, {"size": 1630, "mtime": 1749486926663, "results": "237", "hashOfConfig": "122"}, {"size": 758, "mtime": 1749564354449, "results": "238", "hashOfConfig": "122"}, {"size": 1923, "mtime": 1749564369807, "results": "239", "hashOfConfig": "122"}, {"size": 9324, "mtime": 1749705539683, "results": "240", "hashOfConfig": "122"}, {"size": 7865, "mtime": 1749704535369, "results": "241", "hashOfConfig": "122"}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1ogngm8", {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "272", "messages": "273", "suppressedMessages": "274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "275", "messages": "276", "suppressedMessages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "278", "messages": "279", "suppressedMessages": "280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "281", "messages": "282", "suppressedMessages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "284", "messages": "285", "suppressedMessages": "286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "287", "messages": "288", "suppressedMessages": "289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "290", "messages": "291", "suppressedMessages": "292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "293", "messages": "294", "suppressedMessages": "295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "296", "messages": "297", "suppressedMessages": "298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "299", "messages": "300", "suppressedMessages": "301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "302", "messages": "303", "suppressedMessages": "304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "305", "messages": "306", "suppressedMessages": "307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "308", "messages": "309", "suppressedMessages": "310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "311", "messages": "312", "suppressedMessages": "313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "314", "messages": "315", "suppressedMessages": "316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "317", "messages": "318", "suppressedMessages": "319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "320", "messages": "321", "suppressedMessages": "322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "323", "messages": "324", "suppressedMessages": "325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "326", "messages": "327", "suppressedMessages": "328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "329", "messages": "330", "suppressedMessages": "331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "332", "messages": "333", "suppressedMessages": "334", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "335", "messages": "336", "suppressedMessages": "337", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "338", "messages": "339", "suppressedMessages": "340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "341", "messages": "342", "suppressedMessages": "343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "344", "messages": "345", "suppressedMessages": "346", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "347", "messages": "348", "suppressedMessages": "349", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "350", "messages": "351", "suppressedMessages": "352", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "353", "messages": "354", "suppressedMessages": "355", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "356", "messages": "357", "suppressedMessages": "358", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "359", "messages": "360", "suppressedMessages": "361", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "362", "messages": "363", "suppressedMessages": "364", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "365", "messages": "366", "suppressedMessages": "367", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "368", "messages": "369", "suppressedMessages": "370", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "371", "messages": "372", "suppressedMessages": "373", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "374", "messages": "375", "suppressedMessages": "376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "377", "messages": "378", "suppressedMessages": "379", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "380", "messages": "381", "suppressedMessages": "382", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "383", "messages": "384", "suppressedMessages": "385", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "386", "messages": "387", "suppressedMessages": "388", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "389", "messages": "390", "suppressedMessages": "391", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "392", "messages": "393", "suppressedMessages": "394", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "395", "messages": "396", "suppressedMessages": "397", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "398", "messages": "399", "suppressedMessages": "400", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "401", "messages": "402", "suppressedMessages": "403", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "404", "messages": "405", "suppressedMessages": "406", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "407", "messages": "408", "suppressedMessages": "409", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "410", "messages": "411", "suppressedMessages": "412", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "413", "messages": "414", "suppressedMessages": "415", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "416", "messages": "417", "suppressedMessages": "418", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "419", "messages": "420", "suppressedMessages": "421", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "422", "messages": "423", "suppressedMessages": "424", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "425", "messages": "426", "suppressedMessages": "427", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "428", "messages": "429", "suppressedMessages": "430", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "431", "messages": "432", "suppressedMessages": "433", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "434", "messages": "435", "suppressedMessages": "436", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "437", "messages": "438", "suppressedMessages": "439", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "440", "messages": "441", "suppressedMessages": "442", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "443", "messages": "444", "suppressedMessages": "445", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "446", "messages": "447", "suppressedMessages": "448", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "449", "messages": "450", "suppressedMessages": "451", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "452", "messages": "453", "suppressedMessages": "454", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "455", "messages": "456", "suppressedMessages": "457", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "458", "messages": "459", "suppressedMessages": "460", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "461", "messages": "462", "suppressedMessages": "463", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "464", "messages": "465", "suppressedMessages": "466", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "467", "messages": "468", "suppressedMessages": "469", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "470", "messages": "471", "suppressedMessages": "472", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "473", "messages": "474", "suppressedMessages": "475", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "476", "messages": "477", "suppressedMessages": "478", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "479", "messages": "480", "suppressedMessages": "481", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "482", "messages": "483", "suppressedMessages": "484", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "485", "messages": "486", "suppressedMessages": "487", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "488", "messages": "489", "suppressedMessages": "490", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "491", "messages": "492", "suppressedMessages": "493", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "494", "messages": "495", "suppressedMessages": "496", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "497", "messages": "498", "suppressedMessages": "499", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "500", "messages": "501", "suppressedMessages": "502", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "503", "messages": "504", "suppressedMessages": "505", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "506", "messages": "507", "suppressedMessages": "508", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "509", "messages": "510", "suppressedMessages": "511", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "512", "messages": "513", "suppressedMessages": "514", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "515", "messages": "516", "suppressedMessages": "517", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "518", "messages": "519", "suppressedMessages": "520", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "521", "messages": "522", "suppressedMessages": "523", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "524", "messages": "525", "suppressedMessages": "526", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "527", "messages": "528", "suppressedMessages": "529", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "530", "messages": "531", "suppressedMessages": "532", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "533", "messages": "534", "suppressedMessages": "535", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "536", "messages": "537", "suppressedMessages": "538", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "539", "messages": "540", "suppressedMessages": "541", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "542", "messages": "543", "suppressedMessages": "544", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "545", "messages": "546", "suppressedMessages": "547", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "548", "messages": "549", "suppressedMessages": "550", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "551", "messages": "552", "suppressedMessages": "553", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "554", "messages": "555", "suppressedMessages": "556", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "557", "messages": "558", "suppressedMessages": "559", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "560", "messages": "561", "suppressedMessages": "562", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "563", "messages": "564", "suppressedMessages": "565", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "566", "messages": "567", "suppressedMessages": "568", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "569", "messages": "570", "suppressedMessages": "571", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "572", "messages": "573", "suppressedMessages": "574", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "575", "messages": "576", "suppressedMessages": "577", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "578", "messages": "579", "suppressedMessages": "580", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "581", "messages": "582", "suppressedMessages": "583", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "584", "messages": "585", "suppressedMessages": "586", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "587", "messages": "588", "suppressedMessages": "589", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "590", "messages": "591", "suppressedMessages": "592", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "593", "messages": "594", "suppressedMessages": "595", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "596", "messages": "597", "suppressedMessages": "598", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "599", "messages": "600", "suppressedMessages": "601", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\admin\\activity\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\admin\\analytics\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\admin\\archive\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\admin\\backup\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\admin\\calendar\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\admin\\charts\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\admin\\charts-demo\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\admin\\communications\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\admin\\database\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\admin\\departments\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\admin\\documents\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\admin\\email-test\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\admin\\files\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\admin\\filters\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\admin\\import-export\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\admin\\layout.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\admin\\locations\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\admin\\media\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\admin\\messages\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\admin\\monitor\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\admin\\notifications\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\admin\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\admin\\personnel\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\admin\\personnel\\redux-example.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\admin\\personnel\\[id]\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\admin\\print\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\admin\\reports\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\admin\\search\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\admin\\security\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\admin\\settings\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\admin\\statistics\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\admin\\users\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\api\\chat\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\api\\check-demo-user\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\api\\cloudinary\\media\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\api\\cloudinary\\sync\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\api\\cloudinary\\upload\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\api\\cloudinary\\webhook\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\api\\create-demo-user\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\api\\debug-database\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\api\\email\\send\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\api\\email\\test\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\api\\fix-database\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\api\\fix-demo-user\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\api\\personnel\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\api\\personnel\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\api\\setup-media-db\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\api\\test-cloudinary\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\api\\users\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\api\\users\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\auth\\auth-code-error\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\auth\\confirm\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\auth\\login\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\auth\\register\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\direct-echarts-test\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\health\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\quick-setup\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\test-cloudinary\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\test-cloudinary-image\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\test-echarts\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\test-media\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\test-media-sync\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\app\\test-redux\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\components\\auth\\AuthGuard.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\components\\charts\\BarChart.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\components\\charts\\ChartWrapper.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\components\\charts\\index.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\components\\charts\\LineChart.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\components\\charts\\PieChart.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\components\\chatbot\\ChatBot.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\components\\chatbot\\ChatBubble.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\components\\chatbot\\ChatInterface.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\components\\chatbot\\ChatMessage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\components\\ClientOnly.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\components\\CloudinaryImage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\components\\CloudinaryUploadWidget.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\components\\DeleteConfirmModal.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\components\\DevIndicators.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\components\\Layout\\DashboardLayout.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\components\\Layout\\Header.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\components\\Layout\\Sidebar.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\components\\PersonnelDeleteModal.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\components\\PersonnelModal.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\components\\PersonnelViewModal.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\components\\PlaceholderPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\components\\providers\\ReduxProvider.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\components\\providers\\SupabaseAuthProvider.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\components\\UserModal.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\components\\UserViewModal.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\lib\\bidirectionalSyncService.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\lib\\chatbot-rules.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\lib\\cloudinary.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\lib\\cloudinaryClient.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\lib\\database.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\lib\\database.types.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\lib\\db.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\lib\\email-templates\\NotificationEmailTemplate.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\lib\\email-templates\\SystemAlertEmailTemplate.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\lib\\email-templates\\WelcomeEmailTemplate.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\lib\\emailService.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\lib\\gemini.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\lib\\mediaLibraryService.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\lib\\mockData.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\lib\\redux\\hooks.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\lib\\redux\\slices\\authSlice.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\lib\\redux\\slices\\personnelSlice.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\lib\\redux\\slices\\settingsSlice.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\lib\\redux\\slices\\uiSlice.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\lib\\redux\\slices\\usersSlice.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\lib\\store.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\lib\\supabase.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\lib\\supabaseMediaService.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\lib\\supabaseService.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\lib\\supabaseTest.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\utils\\supabase\\client.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\utils\\supabase\\server.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\utils\\tailwind-health-check.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\lgu-project-app\\src\\utils\\tailwind-optimizer.ts", [], []]